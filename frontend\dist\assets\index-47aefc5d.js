(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function cr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const re={},Ht=[],qe=()=>{},Bl=()=>!1,ts=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ur=e=>e.startsWith("onUpdate:"),ye=Object.assign,fr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Dl=Object.prototype.hasOwnProperty,te=(e,t)=>Dl.call(e,t),q=Array.isArray,qt=e=>ns(e)==="[object Map]",Jo=e=>ns(e)==="[object Set]",K=e=>typeof e=="function",fe=e=>typeof e=="string",ut=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",Go=e=>(ue(e)||K(e))&&K(e.then)&&K(e.catch),Xo=Object.prototype.toString,ns=e=>Xo.call(e),Ul=e=>ns(e).slice(8,-1),Yo=e=>ns(e)==="[object Object]",dr=e=>fe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,an=cr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ss=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Hl=/-(\w)/g,xt=ss(e=>e.replace(Hl,(t,n)=>n?n.toUpperCase():"")),ql=/\B([A-Z])/g,_t=ss(e=>e.replace(ql,"-$1").toLowerCase()),Zo=ss(e=>e.charAt(0).toUpperCase()+e.slice(1)),Es=ss(e=>e?`on${Zo(e)}`:""),bt=(e,t)=>!Object.is(e,t),Fn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ei=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Us=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Hr;const rs=()=>Hr||(Hr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function os(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=fe(s)?Wl(s):os(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(fe(e)||ue(e))return e}const Vl=/;(?![^(]*\))/g,zl=/:([^]+)/,Kl=/\/\*[^]*?\*\//g;function Wl(e){const t={};return e.replace(Kl,"").split(Vl).forEach(n=>{if(n){const s=n.split(zl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function ge(e){let t="";if(fe(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const s=ge(e[n]);s&&(t+=s+" ")}else if(ue(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ql="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Jl=cr(Ql);function ti(e){return!!e||e===""}const ni=e=>!!(e&&e.__v_isRef===!0),ce=e=>fe(e)?e:e==null?"":q(e)||ue(e)&&(e.toString===Xo||!K(e.toString))?ni(e)?ce(e.value):JSON.stringify(e,si,2):String(e),si=(e,t)=>ni(t)?si(e,t.value):qt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Rs(s,o)+" =>"]=r,n),{})}:Jo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Rs(n))}:ut(t)?Rs(t):ue(t)&&!q(t)&&!Yo(t)?String(t):t,Rs=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ne;class Gl{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ne,!t&&Ne&&(this.index=(Ne.scopes||(Ne.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ne;try{return Ne=this,t()}finally{Ne=n}}}on(){Ne=this}off(){Ne=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Xl(){return Ne}let le;const Cs=new WeakSet;class ri{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ne&&Ne.active&&Ne.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Cs.has(this)&&(Cs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ii(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,qr(this),li(this);const t=le,n=Ve;le=this,Ve=!0;try{return this.fn()}finally{ai(this),le=t,Ve=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)mr(t);this.deps=this.depsTail=void 0,qr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Cs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Hs(this)&&this.run()}get dirty(){return Hs(this)}}let oi=0,cn,un;function ii(e,t=!1){if(e.flags|=8,t){e.next=un,un=e;return}e.next=cn,cn=e}function pr(){oi++}function hr(){if(--oi>0)return;if(un){let t=un;for(un=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;cn;){let t=cn;for(cn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function li(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ai(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),mr(s),Yl(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Hs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ci(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ci(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===gn))return;e.globalVersion=gn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Hs(e)){e.flags&=-3;return}const n=le,s=Ve;le=e,Ve=!0;try{li(e);const r=e.fn(e._value);(t.version===0||bt(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{le=n,Ve=s,ai(e),e.flags&=-3}}function mr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)mr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Yl(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ve=!0;const ui=[];function St(){ui.push(Ve),Ve=!1}function Et(){const e=ui.pop();Ve=e===void 0?!0:e}function qr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=le;le=void 0;try{t()}finally{le=n}}}let gn=0;class Zl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class gr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!le||!Ve||le===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==le)n=this.activeLink=new Zl(le,this),le.deps?(n.prevDep=le.depsTail,le.depsTail.nextDep=n,le.depsTail=n):le.deps=le.depsTail=n,fi(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=le.depsTail,n.nextDep=void 0,le.depsTail.nextDep=n,le.depsTail=n,le.deps===n&&(le.deps=s)}return n}trigger(t){this.version++,gn++,this.notify(t)}notify(t){pr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{hr()}}}function fi(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)fi(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const qs=new WeakMap,Pt=Symbol(""),Vs=Symbol(""),yn=Symbol("");function _e(e,t,n){if(Ve&&le){let s=qs.get(e);s||qs.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new gr),r.map=s,r.key=n),r.track()}}function at(e,t,n,s,r,o){const i=qs.get(e);if(!i){gn++;return}const l=a=>{a&&a.trigger()};if(pr(),t==="clear")i.forEach(l);else{const a=q(e),u=a&&dr(n);if(a&&n==="length"){const c=Number(s);i.forEach((f,m)=>{(m==="length"||m===yn||!ut(m)&&m>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(yn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Pt)),qt(e)&&l(i.get(Vs)));break;case"delete":a||(l(i.get(Pt)),qt(e)&&l(i.get(Vs)));break;case"set":qt(e)&&l(i.get(Pt));break}}hr()}function It(e){const t=ee(e);return t===e?t:(_e(t,"iterate",yn),De(e)?t:t.map(Se))}function is(e){return _e(e=ee(e),"iterate",yn),e}const ea={__proto__:null,[Symbol.iterator](){return As(this,Symbol.iterator,Se)},concat(...e){return It(this).concat(...e.map(t=>q(t)?It(t):t))},entries(){return As(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return ot(this,"every",e,t,void 0,arguments)},filter(e,t){return ot(this,"filter",e,t,n=>n.map(Se),arguments)},find(e,t){return ot(this,"find",e,t,Se,arguments)},findIndex(e,t){return ot(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ot(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return ot(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ot(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ts(this,"includes",e)},indexOf(...e){return Ts(this,"indexOf",e)},join(e){return It(this).join(e)},lastIndexOf(...e){return Ts(this,"lastIndexOf",e)},map(e,t){return ot(this,"map",e,t,void 0,arguments)},pop(){return tn(this,"pop")},push(...e){return tn(this,"push",e)},reduce(e,...t){return Vr(this,"reduce",e,t)},reduceRight(e,...t){return Vr(this,"reduceRight",e,t)},shift(){return tn(this,"shift")},some(e,t){return ot(this,"some",e,t,void 0,arguments)},splice(...e){return tn(this,"splice",e)},toReversed(){return It(this).toReversed()},toSorted(e){return It(this).toSorted(e)},toSpliced(...e){return It(this).toSpliced(...e)},unshift(...e){return tn(this,"unshift",e)},values(){return As(this,"values",Se)}};function As(e,t,n){const s=is(e),r=s[t]();return s!==e&&!De(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const ta=Array.prototype;function ot(e,t,n,s,r,o){const i=is(e),l=i!==e&&!De(e),a=i[t];if(a!==ta[t]){const f=a.apply(e,o);return l?Se(f):f}let u=n;i!==e&&(l?u=function(f,m){return n.call(this,Se(f),m,e)}:n.length>2&&(u=function(f,m){return n.call(this,f,m,e)}));const c=a.call(i,u,s);return l&&r?r(c):c}function Vr(e,t,n,s){const r=is(e);let o=n;return r!==e&&(De(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,Se(l),a,e)}),r[t](o,...s)}function Ts(e,t,n){const s=ee(e);_e(s,"iterate",yn);const r=s[t](...n);return(r===-1||r===!1)&&vr(n[0])?(n[0]=ee(n[0]),s[t](...n)):r}function tn(e,t,n=[]){St(),pr();const s=ee(e)[t].apply(e,n);return hr(),Et(),s}const na=cr("__proto__,__v_isRef,__isVue"),di=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function sa(e){ut(e)||(e=String(e));const t=ee(this);return _e(t,"has",e),t.hasOwnProperty(e)}class pi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?pa:yi:o?gi:mi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=q(t);if(!r){let a;if(i&&(a=ea[n]))return a;if(n==="hasOwnProperty")return sa}const l=Reflect.get(t,n,ve(t)?t:s);return(ut(n)?di.has(n):na(n))||(r||_e(t,"get",n),o)?l:ve(l)?i&&dr(n)?l:l.value:ue(l)?r?vi(l):ls(l):l}}class hi extends pi{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const a=$t(o);if(!De(s)&&!$t(s)&&(o=ee(o),s=ee(s)),!q(t)&&ve(o)&&!ve(s))return a?!1:(o.value=s,!0)}const i=q(t)&&dr(n)?Number(n)<t.length:te(t,n),l=Reflect.set(t,n,s,ve(t)?t:r);return t===ee(r)&&(i?bt(s,o)&&at(t,"set",n,s):at(t,"add",n,s)),l}deleteProperty(t,n){const s=te(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&at(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ut(n)||!di.has(n))&&_e(t,"has",n),s}ownKeys(t){return _e(t,"iterate",q(t)?"length":Pt),Reflect.ownKeys(t)}}class ra extends pi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const oa=new hi,ia=new ra,la=new hi(!0);const zs=e=>e,kn=e=>Reflect.getPrototypeOf(e);function aa(e,t,n){return function(...s){const r=this.__v_raw,o=ee(r),i=qt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=r[e](...s),c=n?zs:t?Ks:Se;return!t&&_e(o,"iterate",a?Vs:Pt),{next(){const{value:f,done:m}=u.next();return m?{value:f,done:m}:{value:l?[c(f[0]),c(f[1])]:c(f),done:m}},[Symbol.iterator](){return this}}}}function Pn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ca(e,t){const n={get(r){const o=this.__v_raw,i=ee(o),l=ee(r);e||(bt(r,l)&&_e(i,"get",r),_e(i,"get",l));const{has:a}=kn(i),u=t?zs:e?Ks:Se;if(a.call(i,r))return u(o.get(r));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&_e(ee(r),"iterate",Pt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ee(o),l=ee(r);return e||(bt(r,l)&&_e(i,"has",r),_e(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=ee(l),u=t?zs:e?Ks:Se;return!e&&_e(a,"iterate",Pt),l.forEach((c,f)=>r.call(o,u(c),u(f),i))}};return ye(n,e?{add:Pn("add"),set:Pn("set"),delete:Pn("delete"),clear:Pn("clear")}:{add(r){!t&&!De(r)&&!$t(r)&&(r=ee(r));const o=ee(this);return kn(o).has.call(o,r)||(o.add(r),at(o,"add",r,r)),this},set(r,o){!t&&!De(o)&&!$t(o)&&(o=ee(o));const i=ee(this),{has:l,get:a}=kn(i);let u=l.call(i,r);u||(r=ee(r),u=l.call(i,r));const c=a.call(i,r);return i.set(r,o),u?bt(o,c)&&at(i,"set",r,o):at(i,"add",r,o),this},delete(r){const o=ee(this),{has:i,get:l}=kn(o);let a=i.call(o,r);a||(r=ee(r),a=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return a&&at(o,"delete",r,void 0),u},clear(){const r=ee(this),o=r.size!==0,i=r.clear();return o&&at(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=aa(r,e,t)}),n}function yr(e,t){const n=ca(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(te(n,r)&&r in s?n:s,r,o)}const ua={get:yr(!1,!1)},fa={get:yr(!1,!0)},da={get:yr(!0,!1)};const mi=new WeakMap,gi=new WeakMap,yi=new WeakMap,pa=new WeakMap;function ha(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ma(e){return e.__v_skip||!Object.isExtensible(e)?0:ha(Ul(e))}function ls(e){return $t(e)?e:br(e,!1,oa,ua,mi)}function bi(e){return br(e,!1,la,fa,gi)}function vi(e){return br(e,!0,ia,da,yi)}function br(e,t,n,s,r){if(!ue(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=r.get(e);if(o)return o;const i=ma(e);if(i===0)return e;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function Vt(e){return $t(e)?Vt(e.__v_raw):!!(e&&e.__v_isReactive)}function $t(e){return!!(e&&e.__v_isReadonly)}function De(e){return!!(e&&e.__v_isShallow)}function vr(e){return e?!!e.__v_raw:!1}function ee(e){const t=e&&e.__v_raw;return t?ee(t):e}function ga(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&ei(e,"__v_skip",!0),e}const Se=e=>ue(e)?ls(e):e,Ks=e=>ue(e)?vi(e):e;function ve(e){return e?e.__v_isRef===!0:!1}function pe(e){return xi(e,!1)}function ya(e){return xi(e,!0)}function xi(e,t){return ve(e)?e:new ba(e,t)}class ba{constructor(t,n){this.dep=new gr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ee(t),this._value=n?t:Se(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||De(t)||$t(t);t=s?t:ee(t),bt(t,n)&&(this._rawValue=t,this._value=s?t:Se(t),this.dep.trigger())}}function W(e){return ve(e)?e.value:e}const va={get:(e,t,n)=>t==="__v_raw"?e:W(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ve(r)&&!ve(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function wi(e){return Vt(e)?e:new Proxy(e,va)}class xa{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new gr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=gn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&le!==this)return ii(this,!0),!0}get value(){const t=this.dep.track();return ci(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function wa(e,t,n=!1){let s,r;return K(e)?s=e:(s=e.get,r=e.set),new xa(s,r,n)}const $n={},qn=new WeakMap;let Tt;function _a(e,t=!1,n=Tt){if(n){let s=qn.get(n);s||qn.set(n,s=[]),s.push(e)}}function Sa(e,t,n=re){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=n,u=N=>r?N:De(N)||r===!1||r===0?ct(N,1):ct(N);let c,f,m,g,v=!1,S=!1;if(ve(e)?(f=()=>e.value,v=De(e)):Vt(e)?(f=()=>u(e),v=!0):q(e)?(S=!0,v=e.some(N=>Vt(N)||De(N)),f=()=>e.map(N=>{if(ve(N))return N.value;if(Vt(N))return u(N);if(K(N))return a?a(N,2):N()})):K(e)?t?f=a?()=>a(e,2):e:f=()=>{if(m){St();try{m()}finally{Et()}}const N=Tt;Tt=c;try{return a?a(e,3,[g]):e(g)}finally{Tt=N}}:f=qe,t&&r){const N=f,V=r===!0?1/0:r;f=()=>ct(N(),V)}const w=Xl(),A=()=>{c.stop(),w&&w.active&&fr(w.effects,c)};if(o&&t){const N=t;t=(...V)=>{N(...V),A()}}let k=S?new Array(e.length).fill($n):$n;const F=N=>{if(!(!(c.flags&1)||!c.dirty&&!N))if(t){const V=c.run();if(r||v||(S?V.some((oe,G)=>bt(oe,k[G])):bt(V,k))){m&&m();const oe=Tt;Tt=c;try{const G=[V,k===$n?void 0:S&&k[0]===$n?[]:k,g];a?a(t,3,G):t(...G),k=V}finally{Tt=oe}}}else c.run()};return l&&l(F),c=new ri(f),c.scheduler=i?()=>i(F,!1):F,g=N=>_a(N,!1,c),m=c.onStop=()=>{const N=qn.get(c);if(N){if(a)a(N,4);else for(const V of N)V();qn.delete(c)}},t?s?F(!0):k=c.run():i?i(F.bind(null,!0),!0):c.run(),A.pause=c.pause.bind(c),A.resume=c.resume.bind(c),A.stop=A,A}function ct(e,t=1/0,n){if(t<=0||!ue(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ve(e))ct(e.value,t,n);else if(q(e))for(let s=0;s<e.length;s++)ct(e[s],t,n);else if(Jo(e)||qt(e))e.forEach(s=>{ct(s,t,n)});else if(Yo(e)){for(const s in e)ct(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ct(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Rn(e,t,n,s){try{return s?e(...s):e()}catch(r){as(r,t,n)}}function st(e,t,n,s){if(K(e)){const r=Rn(e,t,n,s);return r&&Go(r)&&r.catch(o=>{as(o,t,n)}),r}if(q(e)){const r=[];for(let o=0;o<e.length;o++)r.push(st(e[o],t,n,s));return r}}function as(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||re;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){St(),Rn(o,null,10,[e,a,u]),Et();return}}Ea(e,n,r,s,i)}function Ea(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Oe=[];let tt=-1;const zt=[];let mt=null,jt=0;const _i=Promise.resolve();let Vn=null;function xr(e){const t=Vn||_i;return e?t.then(this?e.bind(this):e):t}function Ra(e){let t=tt+1,n=Oe.length;for(;t<n;){const s=t+n>>>1,r=Oe[s],o=bn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function wr(e){if(!(e.flags&1)){const t=bn(e),n=Oe[Oe.length-1];!n||!(e.flags&2)&&t>=bn(n)?Oe.push(e):Oe.splice(Ra(t),0,e),e.flags|=1,Si()}}function Si(){Vn||(Vn=_i.then(Ri))}function Ca(e){q(e)?zt.push(...e):mt&&e.id===-1?mt.splice(jt+1,0,e):e.flags&1||(zt.push(e),e.flags|=1),Si()}function zr(e,t,n=tt+1){for(;n<Oe.length;n++){const s=Oe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Oe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Ei(e){if(zt.length){const t=[...new Set(zt)].sort((n,s)=>bn(n)-bn(s));if(zt.length=0,mt){mt.push(...t);return}for(mt=t,jt=0;jt<mt.length;jt++){const n=mt[jt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}mt=null,jt=0}}const bn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ri(e){const t=qe;try{for(tt=0;tt<Oe.length;tt++){const n=Oe[tt];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Rn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;tt<Oe.length;tt++){const n=Oe[tt];n&&(n.flags&=-2)}tt=-1,Oe.length=0,Ei(),Vn=null,(Oe.length||zt.length)&&Ri()}}let Re=null,Ci=null;function zn(e){const t=Re;return Re=e,Ci=e&&e.type.__scopeId||null,t}function Ut(e,t=Re,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Zr(-1);const o=zn(t);let i;try{i=e(...r)}finally{zn(o),s._d&&Zr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function cs(e,t){if(Re===null)return e;const n=ps(Re),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=re]=t[r];o&&(K(o)&&(o={mounted:o,updated:o}),o.deep&&ct(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Ct(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[s];a&&(St(),st(a,n,8,[e.el,l,e,t]),Et())}}const Aa=Symbol("_vte"),Ta=e=>e.__isTeleport;function _r(e,t){e.shapeFlag&6&&e.component?(e.transition=t,_r(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Ai(e,t){return K(e)?(()=>ye({name:e.name},t,{setup:e}))():e}function Ti(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Kn(e,t,n,s,r=!1){if(q(e)){e.forEach((v,S)=>Kn(v,t&&(q(t)?t[S]:t),n,s,r));return}if(Kt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Kn(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?ps(s.component):s.el,i=r?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===re?l.refs={}:l.refs,f=l.setupState,m=ee(f),g=f===re?()=>!1:v=>te(m,v);if(u!=null&&u!==a&&(fe(u)?(c[u]=null,g(u)&&(f[u]=null)):ve(u)&&(u.value=null)),K(a))Rn(a,l,12,[i,c]);else{const v=fe(a),S=ve(a);if(v||S){const w=()=>{if(e.f){const A=v?g(a)?f[a]:c[a]:a.value;r?q(A)&&fr(A,o):q(A)?A.includes(o)||A.push(o):v?(c[a]=[o],g(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else v?(c[a]=i,g(a)&&(f[a]=i)):S&&(a.value=i,e.k&&(c[e.k]=i))};i?(w.id=-1,Le(w,n)):w()}}}rs().requestIdleCallback;rs().cancelIdleCallback;const Kt=e=>!!e.type.__asyncLoader,Oi=e=>e.type.__isKeepAlive;function Oa(e,t){ki(e,"a",t)}function ka(e,t){ki(e,"da",t)}function ki(e,t,n=ke){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(us(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Oi(r.parent.vnode)&&Pa(s,t,n,r),r=r.parent}}function Pa(e,t,n,s){const r=us(t,e,s,!0);Sr(()=>{fr(s[t],r)},n)}function us(e,t,n=ke,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{St();const l=An(n),a=st(t,n,e,i);return l(),Et(),a});return s?r.unshift(o):r.push(o),o}}const ft=e=>(t,n=ke)=>{(!wn||e==="sp")&&us(e,(...s)=>t(...s),n)},$a=ft("bm"),Ma=ft("m"),Fa=ft("bu"),La=ft("u"),Na=ft("bum"),Sr=ft("um"),Ia=ft("sp"),ja=ft("rtg"),Ba=ft("rtc");function Da(e,t=ke){us("ec",e,t)}const Ua=Symbol.for("v-ndc");function Cn(e,t,n,s){let r;const o=n&&n[s],i=q(e);if(i||fe(e)){const l=i&&Vt(e);let a=!1;l&&(a=!De(e),e=is(e)),r=new Array(e.length);for(let u=0,c=e.length;u<c;u++)r[u]=t(a?Se(e[u]):e[u],u,void 0,o&&o[u])}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o&&o[l])}else if(ue(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o&&o[a]));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];r[a]=t(e[c],c,a,o&&o[a])}}else r=[];return n&&(n[s]=r),r}function Pi(e,t,n={},s,r){if(Re.ce||Re.parent&&Kt(Re.parent)&&Re.parent.ce)return t!=="default"&&(n.name=t),M(),xe(be,null,[Q("slot",n,s&&s())],64);let o=e[t];o&&o._c&&(o._d=!1),M();const i=o&&$i(o(n)),l=n.key||i&&i.key,a=xe(be,{key:(l&&!ut(l)?l:`_${t}`)+(!i&&s?"_fb":"")},i||(s?s():[]),i&&e._===1?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function $i(e){return e.some(t=>xn(t)?!(t.type===wt||t.type===be&&!$i(t.children)):!0)?e:null}const Ws=e=>e?Yi(e)?ps(e):Ws(e.parent):null,fn=ye(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ws(e.parent),$root:e=>Ws(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Er(e),$forceUpdate:e=>e.f||(e.f=()=>{wr(e.update)}),$nextTick:e=>e.n||(e.n=xr.bind(e.proxy)),$watch:e=>ac.bind(e)}),Os=(e,t)=>e!==re&&!e.__isScriptSetup&&te(e,t),Ha={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Os(s,t))return i[t]=1,s[t];if(r!==re&&te(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&te(u,t))return i[t]=3,o[t];if(n!==re&&te(n,t))return i[t]=4,n[t];Qs&&(i[t]=0)}}const c=fn[t];let f,m;if(c)return t==="$attrs"&&_e(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==re&&te(n,t))return i[t]=4,n[t];if(m=a.config.globalProperties,te(m,t))return m[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Os(r,t)?(r[t]=n,!0):s!==re&&te(s,t)?(s[t]=n,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==re&&te(e,i)||Os(t,i)||(l=o[0])&&te(l,i)||te(s,i)||te(fn,i)||te(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:te(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Kr(e){return q(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Qs=!0;function qa(e){const t=Er(e),n=e.proxy,s=e.ctx;Qs=!1,t.beforeCreate&&Wr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:m,beforeUpdate:g,updated:v,activated:S,deactivated:w,beforeDestroy:A,beforeUnmount:k,destroyed:F,unmounted:N,render:V,renderTracked:oe,renderTriggered:G,errorCaptured:Ae,serverPrefetch:Ue,expose:Je,inheritAttrs:dt,components:Rt,directives:Ge,filters:Zt}=t;if(u&&Va(u,s,null),i)for(const se in i){const X=i[se];K(X)&&(s[se]=X.bind(n))}if(r){const se=r.call(n,n);ue(se)&&(e.data=ls(se))}if(Qs=!0,o)for(const se in o){const X=o[se],rt=K(X)?X.bind(n,n):K(X.get)?X.get.bind(n,n):qe,pt=!K(X)&&K(X.set)?X.set.bind(n):qe,Xe=me({get:rt,set:pt});Object.defineProperty(s,se,{enumerable:!0,configurable:!0,get:()=>Xe.value,set:Pe=>Xe.value=Pe})}if(l)for(const se in l)Mi(l[se],s,n,se);if(a){const se=K(a)?a.call(n):a;Reflect.ownKeys(se).forEach(X=>{Ln(X,se[X])})}c&&Wr(c,e,"c");function he(se,X){q(X)?X.forEach(rt=>se(rt.bind(n))):X&&se(X.bind(n))}if(he($a,f),he(Ma,m),he(Fa,g),he(La,v),he(Oa,S),he(ka,w),he(Da,Ae),he(Ba,oe),he(ja,G),he(Na,k),he(Sr,N),he(Ia,Ue),q(Je))if(Je.length){const se=e.exposed||(e.exposed={});Je.forEach(X=>{Object.defineProperty(se,X,{get:()=>n[X],set:rt=>n[X]=rt})})}else e.exposed||(e.exposed={});V&&e.render===qe&&(e.render=V),dt!=null&&(e.inheritAttrs=dt),Rt&&(e.components=Rt),Ge&&(e.directives=Ge),Ue&&Ti(e)}function Va(e,t,n=qe){q(e)&&(e=Js(e));for(const s in e){const r=e[s];let o;ue(r)?"default"in r?o=ze(r.from||s,r.default,!0):o=ze(r.from||s):o=ze(r),ve(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Wr(e,t,n){st(q(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Mi(e,t,n,s){let r=s.includes(".")?Wi(n,s):()=>n[s];if(fe(e)){const o=t[e];K(o)&&vt(r,o)}else if(K(e))vt(r,e.bind(n));else if(ue(e))if(q(e))e.forEach(o=>Mi(o,t,n,s));else{const o=K(e.handler)?e.handler.bind(n):t[e.handler];K(o)&&vt(r,o,e)}}function Er(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(u=>Wn(a,u,i,!0)),Wn(a,t,i)),ue(t)&&o.set(t,a),a}function Wn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Wn(e,o,n,!0),r&&r.forEach(i=>Wn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=za[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const za={data:Qr,props:Jr,emits:Jr,methods:ln,computed:ln,beforeCreate:Te,created:Te,beforeMount:Te,mounted:Te,beforeUpdate:Te,updated:Te,beforeDestroy:Te,beforeUnmount:Te,destroyed:Te,unmounted:Te,activated:Te,deactivated:Te,errorCaptured:Te,serverPrefetch:Te,components:ln,directives:ln,watch:Wa,provide:Qr,inject:Ka};function Qr(e,t){return t?e?function(){return ye(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function Ka(e,t){return ln(Js(e),Js(t))}function Js(e){if(q(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Te(e,t){return e?[...new Set([].concat(e,t))]:t}function ln(e,t){return e?ye(Object.create(null),e,t):t}function Jr(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:ye(Object.create(null),Kr(e),Kr(t??{})):t}function Wa(e,t){if(!e)return t;if(!t)return e;const n=ye(Object.create(null),e);for(const s in t)n[s]=Te(e[s],t[s]);return n}function Fi(){return{app:null,config:{isNativeTag:Bl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Qa=0;function Ja(e,t){return function(s,r=null){K(s)||(s=ye({},s)),r!=null&&!ue(r)&&(r=null);const o=Fi(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:Qa++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Tc,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&K(c.install)?(i.add(c),c.install(u,...f)):K(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,m){if(!a){const g=u._ceVNode||Q(s,r);return g.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),f&&t?t(g,c):e(g,c,m),a=!0,u._container=c,c.__vue_app__=u,ps(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(st(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=Wt;Wt=u;try{return c()}finally{Wt=f}}};return u}}let Wt=null;function Ln(e,t){if(ke){let n=ke.provides;const s=ke.parent&&ke.parent.provides;s===n&&(n=ke.provides=Object.create(s)),n[e]=t}}function ze(e,t,n=!1){const s=ke||Re;if(s||Wt){const r=Wt?Wt._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&K(t)?t.call(s&&s.proxy):t}}const Li={},Ni=()=>Object.create(Li),Ii=e=>Object.getPrototypeOf(e)===Li;function Ga(e,t,n,s=!1){const r={},o=Ni();e.propsDefaults=Object.create(null),ji(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:bi(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Xa(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ee(r),[a]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let m=c[f];if(fs(e.emitsOptions,m))continue;const g=t[m];if(a)if(te(o,m))g!==o[m]&&(o[m]=g,u=!0);else{const v=xt(m);r[v]=Gs(a,l,v,g,e,!1)}else g!==o[m]&&(o[m]=g,u=!0)}}}else{ji(e,t,r,o)&&(u=!0);let c;for(const f in l)(!t||!te(t,f)&&((c=_t(f))===f||!te(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(r[f]=Gs(a,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!te(t,f))&&(delete o[f],u=!0)}u&&at(e.attrs,"set","")}function ji(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(an(a))continue;const u=t[a];let c;r&&te(r,c=xt(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:fs(e.emitsOptions,a)||(!(a in s)||u!==s[a])&&(s[a]=u,i=!0)}if(o){const a=ee(n),u=l||re;for(let c=0;c<o.length;c++){const f=o[c];n[f]=Gs(r,a,f,u[f],e,!te(u,f))}}return i}function Gs(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=te(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&K(a)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const c=An(r);s=u[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===_t(n))&&(s=!0))}return s}const Ya=new WeakMap;function Bi(e,t,n=!1){const s=n?Ya:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!K(e)){const c=f=>{a=!0;const[m,g]=Bi(f,t,!0);ye(i,m),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ue(e)&&s.set(e,Ht),Ht;if(q(o))for(let c=0;c<o.length;c++){const f=xt(o[c]);Gr(f)&&(i[f]=re)}else if(o)for(const c in o){const f=xt(c);if(Gr(f)){const m=o[c],g=i[f]=q(m)||K(m)?{type:m}:ye({},m),v=g.type;let S=!1,w=!0;if(q(v))for(let A=0;A<v.length;++A){const k=v[A],F=K(k)&&k.name;if(F==="Boolean"){S=!0;break}else F==="String"&&(w=!1)}else S=K(v)&&v.name==="Boolean";g[0]=S,g[1]=w,(S||te(g,"default"))&&l.push(f)}}const u=[i,l];return ue(e)&&s.set(e,u),u}function Gr(e){return e[0]!=="$"&&!an(e)}const Di=e=>e[0]==="_"||e==="$stable",Rr=e=>q(e)?e.map(nt):[nt(e)],Za=(e,t,n)=>{if(t._n)return t;const s=Ut((...r)=>Rr(t(...r)),n);return s._c=!1,s},Ui=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Di(r))continue;const o=e[r];if(K(o))t[r]=Za(r,o,s);else if(o!=null){const i=Rr(o);t[r]=()=>i}}},Hi=(e,t)=>{const n=Rr(t);e.slots.default=()=>n},qi=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},ec=(e,t,n)=>{const s=e.slots=Ni();if(e.vnode.shapeFlag&32){const r=t._;r?(qi(s,t,n),n&&ei(s,"_",r,!0)):Ui(t,s)}else t&&Hi(e,t)},tc=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=re;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:qi(r,t,n):(o=!t.$stable,Ui(t,r)),i=t}else t&&(Hi(e,t),i={default:1});if(o)for(const l in r)!Di(l)&&i[l]==null&&delete r[l]},Le=mc;function nc(e){return sc(e)}function sc(e,t){const n=rs();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:m,setScopeId:g=qe,insertStaticContent:v}=e,S=(d,p,y,E=null,x=null,R=null,P=void 0,O=null,T=!!p.dynamicChildren)=>{if(d===p)return;d&&!nn(d,p)&&(E=_(d),Pe(d,x,R,!0),d=null),p.patchFlag===-2&&(T=!1,p.dynamicChildren=null);const{type:C,ref:U,shapeFlag:L}=p;switch(C){case ds:w(d,p,y,E);break;case wt:A(d,p,y,E);break;case Nn:d==null&&k(p,y,E,P);break;case be:Rt(d,p,y,E,x,R,P,O,T);break;default:L&1?V(d,p,y,E,x,R,P,O,T):L&6?Ge(d,p,y,E,x,R,P,O,T):(L&64||L&128)&&C.process(d,p,y,E,x,R,P,O,T,j)}U!=null&&x&&Kn(U,d&&d.ref,R,p||d,!p)},w=(d,p,y,E)=>{if(d==null)s(p.el=l(p.children),y,E);else{const x=p.el=d.el;p.children!==d.children&&u(x,p.children)}},A=(d,p,y,E)=>{d==null?s(p.el=a(p.children||""),y,E):p.el=d.el},k=(d,p,y,E)=>{[d.el,d.anchor]=v(d.children,p,y,E,d.el,d.anchor)},F=({el:d,anchor:p},y,E)=>{let x;for(;d&&d!==p;)x=m(d),s(d,y,E),d=x;s(p,y,E)},N=({el:d,anchor:p})=>{let y;for(;d&&d!==p;)y=m(d),r(d),d=y;r(p)},V=(d,p,y,E,x,R,P,O,T)=>{p.type==="svg"?P="svg":p.type==="math"&&(P="mathml"),d==null?oe(p,y,E,x,R,P,O,T):Ue(d,p,x,R,P,O,T)},oe=(d,p,y,E,x,R,P,O)=>{let T,C;const{props:U,shapeFlag:L,transition:B,dirs:H}=d;if(T=d.el=i(d.type,R,U&&U.is,U),L&8?c(T,d.children):L&16&&Ae(d.children,T,null,E,x,ks(d,R),P,O),H&&Ct(d,null,E,"created"),G(T,d,d.scopeId,P,E),U){for(const ie in U)ie!=="value"&&!an(ie)&&o(T,ie,null,U[ie],R,E);"value"in U&&o(T,"value",null,U.value,R),(C=U.onVnodeBeforeMount)&&Ze(C,E,d)}H&&Ct(d,null,E,"beforeMount");const J=rc(x,B);J&&B.beforeEnter(T),s(T,p,y),((C=U&&U.onVnodeMounted)||J||H)&&Le(()=>{C&&Ze(C,E,d),J&&B.enter(T),H&&Ct(d,null,E,"mounted")},x)},G=(d,p,y,E,x)=>{if(y&&g(d,y),E)for(let R=0;R<E.length;R++)g(d,E[R]);if(x){let R=x.subTree;if(p===R||Ji(R.type)&&(R.ssContent===p||R.ssFallback===p)){const P=x.vnode;G(d,P,P.scopeId,P.slotScopeIds,x.parent)}}},Ae=(d,p,y,E,x,R,P,O,T=0)=>{for(let C=T;C<d.length;C++){const U=d[C]=O?gt(d[C]):nt(d[C]);S(null,U,p,y,E,x,R,P,O)}},Ue=(d,p,y,E,x,R,P)=>{const O=p.el=d.el;let{patchFlag:T,dynamicChildren:C,dirs:U}=p;T|=d.patchFlag&16;const L=d.props||re,B=p.props||re;let H;if(y&&At(y,!1),(H=B.onVnodeBeforeUpdate)&&Ze(H,y,p,d),U&&Ct(p,d,y,"beforeUpdate"),y&&At(y,!0),(L.innerHTML&&B.innerHTML==null||L.textContent&&B.textContent==null)&&c(O,""),C?Je(d.dynamicChildren,C,O,y,E,ks(p,x),R):P||X(d,p,O,null,y,E,ks(p,x),R,!1),T>0){if(T&16)dt(O,L,B,y,x);else if(T&2&&L.class!==B.class&&o(O,"class",null,B.class,x),T&4&&o(O,"style",L.style,B.style,x),T&8){const J=p.dynamicProps;for(let ie=0;ie<J.length;ie++){const ne=J[ie],$e=L[ne],we=B[ne];(we!==$e||ne==="value")&&o(O,ne,$e,we,x,y)}}T&1&&d.children!==p.children&&c(O,p.children)}else!P&&C==null&&dt(O,L,B,y,x);((H=B.onVnodeUpdated)||U)&&Le(()=>{H&&Ze(H,y,p,d),U&&Ct(p,d,y,"updated")},E)},Je=(d,p,y,E,x,R,P)=>{for(let O=0;O<p.length;O++){const T=d[O],C=p[O],U=T.el&&(T.type===be||!nn(T,C)||T.shapeFlag&70)?f(T.el):y;S(T,C,U,null,E,x,R,P,!0)}},dt=(d,p,y,E,x)=>{if(p!==y){if(p!==re)for(const R in p)!an(R)&&!(R in y)&&o(d,R,p[R],null,x,E);for(const R in y){if(an(R))continue;const P=y[R],O=p[R];P!==O&&R!=="value"&&o(d,R,O,P,x,E)}"value"in y&&o(d,"value",p.value,y.value,x)}},Rt=(d,p,y,E,x,R,P,O,T)=>{const C=p.el=d?d.el:l(""),U=p.anchor=d?d.anchor:l("");let{patchFlag:L,dynamicChildren:B,slotScopeIds:H}=p;H&&(O=O?O.concat(H):H),d==null?(s(C,y,E),s(U,y,E),Ae(p.children||[],y,U,x,R,P,O,T)):L>0&&L&64&&B&&d.dynamicChildren?(Je(d.dynamicChildren,B,y,x,R,P,O),(p.key!=null||x&&p===x.subTree)&&Vi(d,p,!0)):X(d,p,y,U,x,R,P,O,T)},Ge=(d,p,y,E,x,R,P,O,T)=>{p.slotScopeIds=O,d==null?p.shapeFlag&512?x.ctx.activate(p,y,E,P,T):Zt(p,y,E,x,R,P,T):Ft(d,p,T)},Zt=(d,p,y,E,x,R,P)=>{const O=d.component=_c(d,E,x);if(Oi(d)&&(O.ctx.renderer=j),Sc(O,!1,P),O.asyncDep){if(x&&x.registerDep(O,he,P),!d.el){const T=O.subTree=Q(wt);A(null,T,p,y)}}else he(O,d,p,y,x,R,P)},Ft=(d,p,y)=>{const E=p.component=d.component;if(pc(d,p,y))if(E.asyncDep&&!E.asyncResolved){se(E,p,y);return}else E.next=p,E.update();else p.el=d.el,E.vnode=p},he=(d,p,y,E,x,R,P)=>{const O=()=>{if(d.isMounted){let{next:L,bu:B,u:H,parent:J,vnode:ie}=d;{const Me=zi(d);if(Me){L&&(L.el=ie.el,se(d,L,P)),Me.asyncDep.then(()=>{d.isUnmounted||O()});return}}let ne=L,$e;At(d,!1),L?(L.el=ie.el,se(d,L,P)):L=ie,B&&Fn(B),($e=L.props&&L.props.onVnodeBeforeUpdate)&&Ze($e,J,L,ie),At(d,!0);const we=Ps(d),He=d.subTree;d.subTree=we,S(He,we,f(He.el),_(He),d,x,R),L.el=we.el,ne===null&&hc(d,we.el),H&&Le(H,x),($e=L.props&&L.props.onVnodeUpdated)&&Le(()=>Ze($e,J,L,ie),x)}else{let L;const{el:B,props:H}=p,{bm:J,m:ie,parent:ne,root:$e,type:we}=d,He=Kt(p);if(At(d,!1),J&&Fn(J),!He&&(L=H&&H.onVnodeBeforeMount)&&Ze(L,ne,p),At(d,!0),B&&ae){const Me=()=>{d.subTree=Ps(d),ae(B,d.subTree,d,x,null)};He&&we.__asyncHydrate?we.__asyncHydrate(B,d,Me):Me()}else{$e.ce&&$e.ce._injectChildStyle(we);const Me=d.subTree=Ps(d);S(null,Me,y,E,d,x,R),p.el=Me.el}if(ie&&Le(ie,x),!He&&(L=H&&H.onVnodeMounted)){const Me=p;Le(()=>Ze(L,ne,Me),x)}(p.shapeFlag&256||ne&&Kt(ne.vnode)&&ne.vnode.shapeFlag&256)&&d.a&&Le(d.a,x),d.isMounted=!0,p=y=E=null}};d.scope.on();const T=d.effect=new ri(O);d.scope.off();const C=d.update=T.run.bind(T),U=d.job=T.runIfDirty.bind(T);U.i=d,U.id=d.uid,T.scheduler=()=>wr(U),At(d,!0),C()},se=(d,p,y)=>{p.component=d;const E=d.vnode.props;d.vnode=p,d.next=null,Xa(d,p.props,E,y),tc(d,p.children,y),St(),zr(d),Et()},X=(d,p,y,E,x,R,P,O,T=!1)=>{const C=d&&d.children,U=d?d.shapeFlag:0,L=p.children,{patchFlag:B,shapeFlag:H}=p;if(B>0){if(B&128){pt(C,L,y,E,x,R,P,O,T);return}else if(B&256){rt(C,L,y,E,x,R,P,O,T);return}}H&8?(U&16&&Be(C,x,R),L!==C&&c(y,L)):U&16?H&16?pt(C,L,y,E,x,R,P,O,T):Be(C,x,R,!0):(U&8&&c(y,""),H&16&&Ae(L,y,E,x,R,P,O,T))},rt=(d,p,y,E,x,R,P,O,T)=>{d=d||Ht,p=p||Ht;const C=d.length,U=p.length,L=Math.min(C,U);let B;for(B=0;B<L;B++){const H=p[B]=T?gt(p[B]):nt(p[B]);S(d[B],H,y,null,x,R,P,O,T)}C>U?Be(d,x,R,!0,!1,L):Ae(p,y,E,x,R,P,O,T,L)},pt=(d,p,y,E,x,R,P,O,T)=>{let C=0;const U=p.length;let L=d.length-1,B=U-1;for(;C<=L&&C<=B;){const H=d[C],J=p[C]=T?gt(p[C]):nt(p[C]);if(nn(H,J))S(H,J,y,null,x,R,P,O,T);else break;C++}for(;C<=L&&C<=B;){const H=d[L],J=p[B]=T?gt(p[B]):nt(p[B]);if(nn(H,J))S(H,J,y,null,x,R,P,O,T);else break;L--,B--}if(C>L){if(C<=B){const H=B+1,J=H<U?p[H].el:E;for(;C<=B;)S(null,p[C]=T?gt(p[C]):nt(p[C]),y,J,x,R,P,O,T),C++}}else if(C>B)for(;C<=L;)Pe(d[C],x,R,!0),C++;else{const H=C,J=C,ie=new Map;for(C=J;C<=B;C++){const Fe=p[C]=T?gt(p[C]):nt(p[C]);Fe.key!=null&&ie.set(Fe.key,C)}let ne,$e=0;const we=B-J+1;let He=!1,Me=0;const en=new Array(we);for(C=0;C<we;C++)en[C]=0;for(C=H;C<=L;C++){const Fe=d[C];if($e>=we){Pe(Fe,x,R,!0);continue}let Ye;if(Fe.key!=null)Ye=ie.get(Fe.key);else for(ne=J;ne<=B;ne++)if(en[ne-J]===0&&nn(Fe,p[ne])){Ye=ne;break}Ye===void 0?Pe(Fe,x,R,!0):(en[Ye-J]=C+1,Ye>=Me?Me=Ye:He=!0,S(Fe,p[Ye],y,null,x,R,P,O,T),$e++)}const Dr=He?oc(en):Ht;for(ne=Dr.length-1,C=we-1;C>=0;C--){const Fe=J+C,Ye=p[Fe],Ur=Fe+1<U?p[Fe+1].el:E;en[C]===0?S(null,Ye,y,Ur,x,R,P,O,T):He&&(ne<0||C!==Dr[ne]?Xe(Ye,y,Ur,2):ne--)}}},Xe=(d,p,y,E,x=null)=>{const{el:R,type:P,transition:O,children:T,shapeFlag:C}=d;if(C&6){Xe(d.component.subTree,p,y,E);return}if(C&128){d.suspense.move(p,y,E);return}if(C&64){P.move(d,p,y,j);return}if(P===be){s(R,p,y);for(let L=0;L<T.length;L++)Xe(T[L],p,y,E);s(d.anchor,p,y);return}if(P===Nn){F(d,p,y);return}if(E!==2&&C&1&&O)if(E===0)O.beforeEnter(R),s(R,p,y),Le(()=>O.enter(R),x);else{const{leave:L,delayLeave:B,afterLeave:H}=O,J=()=>s(R,p,y),ie=()=>{L(R,()=>{J(),H&&H()})};B?B(R,J,ie):ie()}else s(R,p,y)},Pe=(d,p,y,E=!1,x=!1)=>{const{type:R,props:P,ref:O,children:T,dynamicChildren:C,shapeFlag:U,patchFlag:L,dirs:B,cacheIndex:H}=d;if(L===-2&&(x=!1),O!=null&&Kn(O,null,y,d,!0),H!=null&&(p.renderCache[H]=void 0),U&256){p.ctx.deactivate(d);return}const J=U&1&&B,ie=!Kt(d);let ne;if(ie&&(ne=P&&P.onVnodeBeforeUnmount)&&Ze(ne,p,d),U&6)On(d.component,y,E);else{if(U&128){d.suspense.unmount(y,E);return}J&&Ct(d,null,p,"beforeUnmount"),U&64?d.type.remove(d,p,y,j,E):C&&!C.hasOnce&&(R!==be||L>0&&L&64)?Be(C,p,y,!1,!0):(R===be&&L&384||!x&&U&16)&&Be(T,p,y),E&&Lt(d)}(ie&&(ne=P&&P.onVnodeUnmounted)||J)&&Le(()=>{ne&&Ze(ne,p,d),J&&Ct(d,null,p,"unmounted")},y)},Lt=d=>{const{type:p,el:y,anchor:E,transition:x}=d;if(p===be){Nt(y,E);return}if(p===Nn){N(d);return}const R=()=>{r(y),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(d.shapeFlag&1&&x&&!x.persisted){const{leave:P,delayLeave:O}=x,T=()=>P(y,R);O?O(d.el,R,T):T()}else R()},Nt=(d,p)=>{let y;for(;d!==p;)y=m(d),r(d),d=y;r(p)},On=(d,p,y)=>{const{bum:E,scope:x,job:R,subTree:P,um:O,m:T,a:C}=d;Xr(T),Xr(C),E&&Fn(E),x.stop(),R&&(R.flags|=8,Pe(P,d,p,y)),O&&Le(O,p),Le(()=>{d.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Be=(d,p,y,E=!1,x=!1,R=0)=>{for(let P=R;P<d.length;P++)Pe(d[P],p,y,E,x)},_=d=>{if(d.shapeFlag&6)return _(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const p=m(d.anchor||d.el),y=p&&p[Aa];return y?m(y):p};let I=!1;const $=(d,p,y)=>{d==null?p._vnode&&Pe(p._vnode,null,null,!0):S(p._vnode||null,d,p,null,null,null,y),p._vnode=d,I||(I=!0,zr(),Ei(),I=!1)},j={p:S,um:Pe,m:Xe,r:Lt,mt:Zt,mc:Ae,pc:X,pbc:Je,n:_,o:e};let Y,ae;return t&&([Y,ae]=t(j)),{render:$,hydrate:Y,createApp:Ja($,Y)}}function ks({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function At({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function rc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Vi(e,t,n=!1){const s=e.children,r=t.children;if(q(s)&&q(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=gt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Vi(i,l)),l.type===ds&&(l.el=i.el)}}function oc(e){const t=e.slice(),n=[0];let s,r,o,i,l;const a=e.length;for(s=0;s<a;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function zi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:zi(t)}function Xr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ic=Symbol.for("v-scx"),lc=()=>ze(ic);function vt(e,t,n){return Ki(e,t,n)}function Ki(e,t,n=re){const{immediate:s,deep:r,flush:o,once:i}=n,l=ye({},n),a=t&&s||!t&&o!=="post";let u;if(wn){if(o==="sync"){const g=lc();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=qe,g.resume=qe,g.pause=qe,g}}const c=ke;l.call=(g,v,S)=>st(g,c,v,S);let f=!1;o==="post"?l.scheduler=g=>{Le(g,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(g,v)=>{v?g():wr(g)}),l.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const m=Sa(e,t,l);return wn&&(u?u.push(m):a&&m()),m}function ac(e,t,n){const s=this.proxy,r=fe(e)?e.includes(".")?Wi(s,e):()=>s[e]:e.bind(s,s);let o;K(t)?o=t:(o=t.handler,n=t);const i=An(this),l=Ki(r,o.bind(s),n);return i(),l}function Wi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const cc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${xt(t)}Modifiers`]||e[`${_t(t)}Modifiers`];function uc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||re;let r=n;const o=t.startsWith("update:"),i=o&&cc(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>fe(c)?c.trim():c)),i.number&&(r=n.map(Us)));let l,a=s[l=Es(t)]||s[l=Es(xt(t))];!a&&o&&(a=s[l=Es(_t(t))]),a&&st(a,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,st(u,e,6,r)}}function Qi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!K(e)){const a=u=>{const c=Qi(u,t,!0);c&&(l=!0,ye(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ue(e)&&s.set(e,null),null):(q(o)?o.forEach(a=>i[a]=null):ye(i,o),ue(e)&&s.set(e,i),i)}function fs(e,t){return!e||!ts(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,_t(t))||te(e,t))}function Ps(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:m,setupState:g,ctx:v,inheritAttrs:S}=e,w=zn(e);let A,k;try{if(n.shapeFlag&4){const N=r||s,V=N;A=nt(u.call(V,N,c,f,g,m,v)),k=l}else{const N=t;A=nt(N.length>1?N(f,{attrs:l,slots:i,emit:a}):N(f,null)),k=t.props?l:fc(l)}}catch(N){dn.length=0,as(N,e,1),A=Q(wt)}let F=A;if(k&&S!==!1){const N=Object.keys(k),{shapeFlag:V}=F;N.length&&V&7&&(o&&N.some(ur)&&(k=dc(k,o)),F=Qt(F,k,!1,!0))}return n.dirs&&(F=Qt(F,null,!1,!0),F.dirs=F.dirs?F.dirs.concat(n.dirs):n.dirs),n.transition&&_r(F,n.transition),A=F,zn(w),A}const fc=e=>{let t;for(const n in e)(n==="class"||n==="style"||ts(n))&&((t||(t={}))[n]=e[n]);return t},dc=(e,t)=>{const n={};for(const s in e)(!ur(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function pc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?Yr(s,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const m=c[f];if(i[m]!==s[m]&&!fs(u,m))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Yr(s,i,u):!0:!!i;return!1}function Yr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!fs(n,o))return!0}return!1}function hc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ji=e=>e.__isSuspense;function mc(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):Ca(e)}const be=Symbol.for("v-fgt"),ds=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Nn=Symbol.for("v-stc"),dn=[];let Ie=null;function M(e=!1){dn.push(Ie=e?null:[])}function gc(){dn.pop(),Ie=dn[dn.length-1]||null}let vn=1;function Zr(e,t=!1){vn+=e,e<0&&Ie&&t&&(Ie.hasOnce=!0)}function Gi(e){return e.dynamicChildren=vn>0?Ie||Ht:null,gc(),vn>0&&Ie&&Ie.push(e),e}function D(e,t,n,s,r,o){return Gi(h(e,t,n,s,r,o,!0))}function xe(e,t,n,s,r){return Gi(Q(e,t,n,s,r,!0))}function xn(e){return e?e.__v_isVNode===!0:!1}function nn(e,t){return e.type===t.type&&e.key===t.key}const Xi=({key:e})=>e??null,In=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?fe(e)||ve(e)||K(e)?{i:Re,r:e,k:t,f:!!n}:e:null);function h(e,t=null,n=null,s=0,r=null,o=e===be?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Xi(t),ref:t&&In(t),scopeId:Ci,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Re};return l?(Ar(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=fe(n)?8:16),vn>0&&!i&&Ie&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Ie.push(a),a}const Q=yc;function yc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Ua)&&(e=wt),xn(e)){const l=Qt(e,t,!0);return n&&Ar(l,n),vn>0&&!o&&Ie&&(l.shapeFlag&6?Ie[Ie.indexOf(e)]=l:Ie.push(l)),l.patchFlag=-2,l}if(Ac(e)&&(e=e.__vccOpts),t){t=bc(t);let{class:l,style:a}=t;l&&!fe(l)&&(t.class=ge(l)),ue(a)&&(vr(a)&&!q(a)&&(a=ye({},a)),t.style=os(a))}const i=fe(e)?1:Ji(e)?128:Ta(e)?64:ue(e)?4:K(e)?2:0;return h(e,t,n,s,r,i,o,!0)}function bc(e){return e?vr(e)||Ii(e)?ye({},e):e:null}function Qt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?vc(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Xi(u),ref:t&&t.ref?n&&o?q(o)?o.concat(In(t)):[o,In(t)]:In(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==be?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qt(e.ssContent),ssFallback:e.ssFallback&&Qt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&_r(c,a.clone(c)),c}function Ot(e=" ",t=0){return Q(ds,null,e,t)}function Cr(e,t){const n=Q(Nn,null,e);return n.staticCount=t,n}function Ce(e="",t=!1){return t?(M(),xe(wt,null,e)):Q(wt,null,e)}function nt(e){return e==null||typeof e=="boolean"?Q(wt):q(e)?Q(be,null,e.slice()):xn(e)?gt(e):Q(ds,null,String(e))}function gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qt(e)}function Ar(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(q(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Ar(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Ii(t)?t._ctx=Re:r===3&&Re&&(Re.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:Re},n=32):(t=String(t),s&64?(n=16,t=[Ot(t)]):n=8);e.children=t,e.shapeFlag|=n}function vc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=ge([t.class,s.class]));else if(r==="style")t.style=os([t.style,s.style]);else if(ts(r)){const o=t[r],i=s[r];i&&o!==i&&!(q(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Ze(e,t,n,s=null){st(e,t,7,[n,s])}const xc=Fi();let wc=0;function _c(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||xc,o={uid:wc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Gl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Bi(s,r),emitsOptions:Qi(s,r),emit:null,emitted:null,propsDefaults:re,inheritAttrs:s.inheritAttrs,ctx:re,data:re,props:re,attrs:re,slots:re,refs:re,setupState:re,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=uc.bind(null,o),e.ce&&e.ce(o),o}let ke=null,Qn,Xs;{const e=rs(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Qn=t("__VUE_INSTANCE_SETTERS__",n=>ke=n),Xs=t("__VUE_SSR_SETTERS__",n=>wn=n)}const An=e=>{const t=ke;return Qn(e),e.scope.on(),()=>{e.scope.off(),Qn(t)}},eo=()=>{ke&&ke.scope.off(),Qn(null)};function Yi(e){return e.vnode.shapeFlag&4}let wn=!1;function Sc(e,t=!1,n=!1){t&&Xs(t);const{props:s,children:r}=e.vnode,o=Yi(e);Ga(e,s,o,t),ec(e,r,n);const i=o?Ec(e,t):void 0;return t&&Xs(!1),i}function Ec(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ha);const{setup:s}=n;if(s){St();const r=e.setupContext=s.length>1?Cc(e):null,o=An(e),i=Rn(s,e,0,[e.props,r]),l=Go(i);if(Et(),o(),(l||e.sp)&&!Kt(e)&&Ti(e),l){if(i.then(eo,eo),t)return i.then(a=>{to(e,a,t)}).catch(a=>{as(a,e,0)});e.asyncDep=i}else to(e,i,t)}else Zi(e,t)}function to(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ue(t)&&(e.setupState=wi(t)),Zi(e,n)}let no;function Zi(e,t,n){const s=e.type;if(!e.render){if(!t&&no&&!s.render){const r=s.template||Er(e).template;if(r){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:a}=s,u=ye(ye({isCustomElement:o,delimiters:l},i),a);s.render=no(r,u)}}e.render=s.render||qe}{const r=An(e);St();try{qa(e)}finally{Et(),r()}}}const Rc={get(e,t){return _e(e,"get",""),e[t]}};function Cc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Rc),slots:e.slots,emit:e.emit,expose:t}}function ps(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(wi(ga(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in fn)return fn[n](e)},has(t,n){return n in t||n in fn}})):e.proxy}function Ac(e){return K(e)&&"__vccOpts"in e}const me=(e,t)=>wa(e,t,wn);function el(e,t,n){const s=arguments.length;return s===2?ue(t)&&!q(t)?xn(t)?Q(e,null,[t]):Q(e,t):Q(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&xn(n)&&(n=[n]),Q(e,t,n))}const Tc="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ys;const so=typeof window<"u"&&window.trustedTypes;if(so)try{Ys=so.createPolicy("vue",{createHTML:e=>e})}catch{}const tl=Ys?e=>Ys.createHTML(e):e=>e,Oc="http://www.w3.org/2000/svg",kc="http://www.w3.org/1998/Math/MathML",lt=typeof document<"u"?document:null,ro=lt&&lt.createElement("template"),Pc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?lt.createElementNS(Oc,e):t==="mathml"?lt.createElementNS(kc,e):n?lt.createElement(e,{is:n}):lt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>lt.createTextNode(e),createComment:e=>lt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>lt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{ro.innerHTML=tl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ro.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},$c=Symbol("_vtc");function Mc(e,t,n){const s=e[$c];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Jn=Symbol("_vod"),nl=Symbol("_vsh"),Tr={beforeMount(e,{value:t},{transition:n}){e[Jn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):sn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),sn(e,!0),s.enter(e)):s.leave(e,()=>{sn(e,!1)}):sn(e,t))},beforeUnmount(e,{value:t}){sn(e,t)}};function sn(e,t){e.style.display=t?e[Jn]:"none",e[nl]=!t}const Fc=Symbol(""),Lc=/(^|;)\s*display\s*:/;function Nc(e,t,n){const s=e.style,r=fe(n);let o=!1;if(n&&!r){if(t)if(fe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&jn(s,l,"")}else for(const i in t)n[i]==null&&jn(s,i,"");for(const i in n)i==="display"&&(o=!0),jn(s,i,n[i])}else if(r){if(t!==n){const i=s[Fc];i&&(n+=";"+i),s.cssText=n,o=Lc.test(n)}}else t&&e.removeAttribute("style");Jn in e&&(e[Jn]=o?s.display:"",e[nl]&&(s.display="none"))}const oo=/\s*!important$/;function jn(e,t,n){if(q(n))n.forEach(s=>jn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ic(e,t);oo.test(n)?e.setProperty(_t(s),n.replace(oo,""),"important"):e[s]=n}}const io=["Webkit","Moz","ms"],$s={};function Ic(e,t){const n=$s[t];if(n)return n;let s=xt(t);if(s!=="filter"&&s in e)return $s[t]=s;s=Zo(s);for(let r=0;r<io.length;r++){const o=io[r]+s;if(o in e)return $s[t]=o}return t}const lo="http://www.w3.org/1999/xlink";function ao(e,t,n,s,r,o=Jl(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(lo,t.slice(6,t.length)):e.setAttributeNS(lo,t,n):n==null||o&&!ti(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ut(n)?String(n):n)}function co(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?tl(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ti(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Bt(e,t,n,s){e.addEventListener(t,n,s)}function jc(e,t,n,s){e.removeEventListener(t,n,s)}const uo=Symbol("_vei");function Bc(e,t,n,s,r=null){const o=e[uo]||(e[uo]={}),i=o[t];if(s&&i)i.value=s;else{const[l,a]=Dc(t);if(s){const u=o[t]=qc(s,r);Bt(e,l,u,a)}else i&&(jc(e,l,i,a),o[t]=void 0)}}const fo=/(?:Once|Passive|Capture)$/;function Dc(e){let t;if(fo.test(e)){t={};let s;for(;s=e.match(fo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_t(e.slice(2)),t]}let Ms=0;const Uc=Promise.resolve(),Hc=()=>Ms||(Uc.then(()=>Ms=0),Ms=Date.now());function qc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;st(Vc(s,n.value),t,5,[s])};return n.value=e,n.attached=Hc(),n}function Vc(e,t){if(q(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const po=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,zc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Mc(e,s,i):t==="style"?Nc(e,n,s):ts(t)?ur(t)||Bc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Kc(e,t,s,i))?(co(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ao(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!fe(s))?co(e,xt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ao(e,t,s,i))};function Kc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&po(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return po(t)&&fe(n)?!1:t in e}const ho=e=>{const t=e.props["onUpdate:modelValue"]||!1;return q(t)?n=>Fn(t,n):t};function Wc(e){e.target.composing=!0}function mo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Fs=Symbol("_assign"),Qc={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Fs]=ho(r);const o=s||r.props&&r.props.type==="number";Bt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=Us(l)),e[Fs](l)}),n&&Bt(e,"change",()=>{e.value=e.value.trim()}),t||(Bt(e,"compositionstart",Wc),Bt(e,"compositionend",mo),Bt(e,"change",mo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Fs]=ho(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Us(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===a)||(e.value=a))}},Jc=["ctrl","shift","alt","meta"],Gc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Jc.some(n=>e[`${n}Key`]&&!t.includes(n))},pn=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Gc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Xc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Yc=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=_t(r.key);if(t.some(i=>i===o||Xc[i]===o))return e(r)})},Zc=ye({patchProp:zc},Pc);let go;function eu(){return go||(go=nc(Zc))}const tu=(...e)=>{const t=eu().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=su(s);if(!r)return;const o=t._component;!K(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,nu(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function nu(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function su(e){return fe(e)?document.querySelector(e):e}const ru={class:"bg-white mt-8 border-t border-gray-200"},ou={class:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8"},iu={class:"text-center text-sm text-gray-500"},lu={__name:"AppFooter",setup(e){const t=me(()=>new Date().getFullYear());return(n,s)=>(M(),D("footer",ru,[h("div",ou,[h("p",iu,"© "+ce(t.value)+" 合同风险分析系统. 保留所有权利.",1)])]))}},hs=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},au={},cu={class:"bg-white shadow-sm"};function uu(e,t){return M(),D("header",cu,t[0]||(t[0]=[h("div",{class:"max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8 flex items-center justify-between"},[h("h1",{class:"text-2xl font-bold text-gray-900"},"合同风险分析系统")],-1)]))}const fu=hs(au,[["render",uu]]),du={class:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8"},pu={class:"bg-white shadow rounded-lg overflow-hidden"},hu={__name:"MainContent",setup(e){return(t,n)=>(M(),D("main",du,[h("div",pu,[Pi(t.$slots,"default")])]))}},mu={class:"mt-4 text-lg font-medium text-gray-900"},gu={key:1,class:"mt-4 flex justify-center space-x-4"},yu={__name:"FileUploader",emits:["file-selected","start-analysis"],setup(e,{emit:t}){const n=t,s=pe(!1),r=pe(null),o=pe(null),i=c=>c?c.type!=="application/pdf"?(alert("请选择PDF文件"),!1):c.size>20*1024*1024?(alert("文件大小不能超过20MB"),!1):!0:!1,l=c=>{const f=c.target.files[0];i(f)&&(r.value=f,n("file-selected",f))},a=c=>{s.value=!1;const f=c.dataTransfer.files[0];i(f)&&(r.value=f,n("file-selected",f))},u=()=>{r.value=null,n("file-selected",null)};return(c,f)=>(M(),D("div",{class:ge(["border-2 border-dashed border-gray-300 rounded-lg p-12 text-center",{"border-blue-500 bg-blue-50":s.value}]),onDragover:f[2]||(f[2]=pn(m=>s.value=!0,["prevent"])),onDragleave:f[3]||(f[3]=pn(m=>s.value=!1,["prevent"])),onDrop:pn(a,["prevent"])},[f[4]||(f[4]=h("div",{class:"flex justify-center"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),h("p",mu,ce(r.value?r.value.name:"拖放文件到此处或点击上传"),1),f[5]||(f[5]=h("p",{class:"mt-2 text-sm text-gray-500"},"支持PDF格式，最大20MB",-1)),h("input",{type:"file",ref_key:"fileInput",ref:o,class:"hidden",accept:"application/pdf",onChange:l},null,544),r.value?Ce("",!0):(M(),D("button",{key:0,type:"button",class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:f[0]||(f[0]=m=>c.$refs.fileInput.click())}," 选择文件 ")),r.value?(M(),D("div",gu,[h("button",{type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:f[1]||(f[1]=m=>c.$emit("start-analysis"))}," 开始分析 "),h("button",{type:"button",class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:u}," 取消 ")])):Ce("",!0)],34))}},bu={class:"p-6"},vu={__name:"FileUploadStep",emits:["file-selected","start-analysis"],setup(e){return(t,n)=>(M(),D("div",bu,[Q(yu,{onFileSelected:n[0]||(n[0]=s=>t.$emit("file-selected",s)),onStartAnalysis:n[1]||(n[1]=s=>t.$emit("start-analysis"))})]))}},xu={key:0,class:"p-6"},wu={class:"text-center"},_u={class:"mt-4 text-lg font-medium text-gray-900"},Su={class:"mt-4 w-full bg-gray-200 rounded-full h-2.5"},Eu={__name:"AnalysisProgress",props:{isAnalyzing:{type:Boolean,required:!0},progressMessage:{type:String,required:!0},analysisProgress:{type:Number,required:!0}},setup(e){return(t,n)=>e.isAnalyzing?(M(),D("div",xu,[h("div",wu,[n[0]||(n[0]=h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-12 w-12 mx-auto text-blue-500 animate-pulse",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})],-1)),h("h2",_u,ce(e.progressMessage),1),h("div",Su,[h("div",{class:"bg-blue-600 h-2.5 rounded-full",style:os({width:`${e.analysisProgress}%`})},null,4)]),n[1]||(n[1]=h("p",{class:"mt-2 text-sm text-gray-500"},"分析可能需要1-2分钟，请耐心等待...",-1))])])):Ce("",!0)}},Ru={__name:"AnalysisStep",props:{isAnalyzing:{type:Boolean,required:!0},progressMessage:{type:String,required:!0},analysisProgress:{type:Number,required:!0}},setup(e){return(t,n)=>(M(),xe(Eu,{"is-analyzing":e.isAnalyzing,"progress-message":e.progressMessage,"analysis-progress":e.analysisProgress},null,8,["is-analyzing","progress-message","analysis-progress"]))}},Cu={class:"mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"},Au={class:"bg-white overflow-hidden shadow rounded-lg"},Tu={class:"p-5"},Ou={class:"flex items-center"},ku={class:"ml-5 w-0 flex-1"},Pu={class:"flex items-baseline"},$u={class:"text-2xl font-semibold text-gray-900"},Mu={class:"bg-white overflow-hidden shadow rounded-lg"},Fu={class:"p-5"},Lu={class:"flex items-center"},Nu={class:"ml-5 w-0 flex-1"},Iu={class:"flex items-baseline"},ju={class:"text-2xl font-semibold text-gray-900"},Bu={class:"bg-white overflow-hidden shadow rounded-lg"},Du={class:"p-5"},Uu={class:"flex items-center"},Hu={class:"ml-5 w-0 flex-1"},qu={class:"flex items-baseline"},Vu={class:"text-2xl font-semibold text-gray-900"},zu={class:"bg-white overflow-hidden shadow rounded-lg"},Ku={class:"p-5"},Wu={class:"flex items-center"},Qu={class:"ml-5 w-0 flex-1"},Ju={class:"flex items-baseline"},Gu={class:"text-2xl font-semibold text-gray-900"},Xu={__name:"AnalysisStats",props:{duration:{type:Number,required:!0},modelResult:{type:String,required:!0}},setup(e){const t=o=>o?`${o.toFixed(2)}秒`:"未知",n=o=>{if(!o)return"未知";const i=o.match(/风险|条款|高风险|建议修改|潜在问题/g);return i?i.length:0},s=o=>o?o.length>3e3?"非常详细":o.length>2e3?"详细":o.length>1e3?"一般":"简略":"未知",r=o=>{if(!o)return"未知";const i=o.match(/建议|修改|协商|更改|调整/g);return i?i.length>15?"非常实用":i.length>10?"较为实用":i.length>5?"一般":"建议较少":"较少建议"};return(o,i)=>(M(),D("div",Cu,[h("div",Au,[h("div",Tu,[h("div",Ou,[i[1]||(i[1]=h("div",{class:"flex-shrink-0 bg-blue-500 rounded-md p-3"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),h("div",ku,[h("dl",null,[i[0]||(i[0]=h("dt",{class:"text-sm font-medium text-gray-500 truncate"},"分析耗时",-1)),h("dd",Pu,[h("div",$u,ce(t(e.duration)),1)])])])])])]),h("div",Mu,[h("div",Fu,[h("div",Lu,[i[3]||(i[3]=h("div",{class:"flex-shrink-0 bg-red-500 rounded-md p-3"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})])],-1)),h("div",Nu,[h("dl",null,[i[2]||(i[2]=h("dt",{class:"text-sm font-medium text-gray-500 truncate"},"风险条款数",-1)),h("dd",Iu,[h("div",ju,ce(n(e.modelResult)),1)])])])])])]),h("div",Bu,[h("div",Du,[h("div",Uu,[i[5]||(i[5]=h("div",{class:"flex-shrink-0 bg-green-500 rounded-md p-3"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),h("div",Hu,[h("dl",null,[i[4]||(i[4]=h("dt",{class:"text-sm font-medium text-gray-500 truncate"},"分析详细程度",-1)),h("dd",qu,[h("div",Vu,ce(s(e.modelResult)),1)])])])])])]),h("div",zu,[h("div",Ku,[h("div",Wu,[i[7]||(i[7]=h("div",{class:"flex-shrink-0 bg-blue-500 rounded-md p-3"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),h("div",Qu,[h("dl",null,[i[6]||(i[6]=h("dt",{class:"text-sm font-medium text-gray-500 truncate"},"建议质量",-1)),h("dd",Ju,[h("div",Gu,ce(r(e.modelResult)),1)])])])])])])]))}},Yu={class:"flex items-center justify-between px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50"},Zu={__name:"QAAssistantHeader",emits:["close"],setup(e){return(t,n)=>(M(),D("div",Yu,[n[2]||(n[2]=Cr('<div class="flex items-center space-x-3"><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"><svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg></div><div><div class="font-semibold text-gray-800">合同问答助手</div><div class="text-xs text-gray-500">AI智能分析</div></div></div>',1)),h("button",{onClick:n[0]||(n[0]=s=>t.$emit("close")),class:"text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-1 transition-colors"},n[1]||(n[1]=[h("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 18L18 6M6 6l12 12"})],-1)]))]))}},ef={class:"mb-6"},tf={class:"space-y-2"},nf=["onClick"],sf={class:"flex items-center justify-between"},rf={__name:"QASuggestedQuestions",props:{questions:{type:Array,default:()=>[]}},emits:["select"],setup(e){return(t,n)=>(M(),D("div",ef,[n[1]||(n[1]=h("div",{class:"flex items-center mb-3"},[h("svg",{class:"w-4 h-4 text-blue-500 mr-2",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),h("span",{class:"font-medium text-gray-700"},"推荐问题")],-1)),h("div",tf,[(M(!0),D(be,null,Cn(e.questions,(s,r)=>(M(),D("button",{key:r,class:"w-full text-left px-4 py-3 bg-white hover:bg-blue-50 hover:border-blue-200 rounded-xl border border-gray-200 text-sm text-gray-700 transition-all duration-200 shadow-sm hover:shadow-md",onClick:o=>t.$emit("select",s)},[h("div",sf,[h("span",null,ce(s),1),n[0]||(n[0]=h("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 5l7 7-7 7"})],-1))])],8,nf))),128))])]))}},of={class:"flex justify-end"},lf={class:"bg-blue-500 text-white px-4 py-2 rounded-2xl rounded-br-md max-w-xs shadow-sm"},af={__name:"UserMessage",props:{content:{type:String,required:!0}},setup(e){return(t,n)=>(M(),D("div",of,[h("div",lf,ce(e.content),1)]))}},cf={class:"flex items-center space-x-1"},uf={__name:"ThinkingIndicator",setup(e){return(t,n)=>(M(),D("div",cf,n[0]||(n[0]=[Cr('<span class="text-gray-600">正在思考</span><div class="flex space-x-1"><div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay:0ms;"></div><div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay:150ms;"></div><div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay:300ms;"></div></div>',2)])))}};function Or(e=!1){const t=pe(e);return{isExpanded:t,toggle:()=>{t.value=!t.value}}}const ff={class:"mt-3"},df={class:"bg-blue-50 border border-blue-200 rounded-2xl p-4 shadow-sm"},pf={class:"space-y-2 transition-all duration-200"},hf=["onClick"],mf={class:"flex items-center justify-between"},gf={class:"flex-1 pr-2"},yf={__name:"FollowUpQuestions",props:{questions:{type:Array,default:()=>[]}},emits:["selectQuestion"],setup(e){const{isExpanded:t,toggle:n}=Or(!1);return(s,r)=>(M(),D("div",ff,[h("div",df,[h("div",{class:"flex items-center mb-3 cursor-pointer",onClick:r[0]||(r[0]=(...o)=>W(n)&&W(n)(...o))},[r[2]||(r[2]=h("svg",{class:"w-4 h-4 text-blue-500 mr-2",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)),r[3]||(r[3]=h("span",{class:"font-medium text-blue-700 text-sm"},"相关问题推荐",-1)),(M(),D("svg",{class:ge(["w-4 h-4 text-blue-500 ml-2 transform transition-transform duration-200",{"rotate-180":W(t)}]),fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},r[1]||(r[1]=[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19 9l-7 7-7-7"},null,-1)]),2))]),cs(h("div",pf,[(M(!0),D(be,null,Cn(e.questions,(o,i)=>(M(),D("button",{key:i,class:"w-full text-left px-3 py-2 bg-white hover:bg-blue-50 hover:border-blue-300 rounded-lg border border-blue-200 text-sm text-gray-700 transition-all duration-200 shadow-sm hover:shadow-md",onClick:l=>s.$emit("selectQuestion",o)},[h("div",mf,[h("span",gf,ce(o),1),r[4]||(r[4]=h("svg",{class:"w-3 h-3 text-blue-400 flex-shrink-0",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 5l7 7-7 7"})],-1))])],8,hf))),128))],512),[[Tr,W(t)]])])]))}},bf={class:"flex items-start space-x-3"},vf={class:"w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0"},xf={key:0,class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},wf={key:1,class:"w-4 h-4 text-white animate-spin",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},_f={class:"flex-1"},Sf={key:0,class:"bg-white px-4 py-2 rounded-2xl rounded-bl-md max-w-xs shadow-sm border border-gray-100"},Ef={key:1},Rf={__name:"AssistantMessage",props:{content:{type:String,default:""},isThinking:{type:Boolean,default:!1},isFollowUp:{type:Boolean,default:!1},followUpQuestions:{type:Array,default:()=>[]}},emits:["selectQuestion"],setup(e){return(t,n)=>(M(),D("div",bf,[h("div",vf,[e.isThinking?(M(),D("svg",wf,n[2]||(n[2]=[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(M(),D("svg",xf,n[1]||(n[1]=[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"},null,-1)])))]),h("div",_f,[e.isFollowUp?(M(),xe(yf,{key:1,questions:e.followUpQuestions,onSelectQuestion:n[0]||(n[0]=s=>t.$emit("selectQuestion",s))},null,8,["questions"])):(M(),D("div",Sf,[e.isThinking?(M(),xe(uf,{key:0})):(M(),D("div",Ef,ce(e.content),1))]))])]))}},Cf={key:0,class:"space-y-4"},Af={__name:"QAMessageList",props:{messages:{type:Array,default:()=>[]}},emits:["selectQuestion"],setup(e){return(t,n)=>e.messages.length?(M(),D("div",Cf,[(M(!0),D(be,null,Cn(e.messages,(s,r)=>(M(),D("div",{key:r},[s.role==="user"?(M(),xe(af,{key:0,content:s.content},null,8,["content"])):(M(),xe(Rf,{key:1,content:s.content,isThinking:s.isThinking,isFollowUp:s.isFollowUp,followUpQuestions:s.followUpQuestions,onSelectQuestion:n[0]||(n[0]=o=>t.$emit("selectQuestion",o))},null,8,["content","isThinking","isFollowUp","followUpQuestions"]))]))),128))])):Ce("",!0)}},Tf={class:"relative"},Of=["onKeydown"],kf=["disabled"],Pf={__name:"QAInputBox",props:{modelValue:{type:String,default:""}},emits:["update:modelValue","send"],setup(e,{emit:t}){const n=e,s=t,r=me({get:()=>n.modelValue,set:i=>s("update:modelValue",i)});function o(){r.value.trim()&&s("send")}return(i,l)=>(M(),D("form",{onSubmit:pn(o,["prevent"])},[h("div",Tf,[cs(h("textarea",{"onUpdate:modelValue":l[0]||(l[0]=a=>r.value=a),rows:"3",class:"w-full border border-gray-200 rounded-2xl px-4 py-3 pr-14 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-gray-400 text-sm",placeholder:"请输入您的问题",onKeydown:Yc(pn(o,["exact","prevent"]),["enter"])},null,40,Of),[[Qc,r.value]]),h("button",{type:"submit",class:"absolute bottom-3 right-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-2.5 rounded-full flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50",disabled:!r.value.trim()},l[1]||(l[1]=[h("svg",{class:"w-4 h-4 transform rotate-45",fill:"none",stroke:"currentColor","stroke-width":"2",viewBox:"0 0 24 24"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})],-1)]),8,kf)]),l[2]||(l[2]=h("div",{class:"mt-2 text-xs text-gray-400 px-1"}," 按 Enter 发送，Ctrl + Enter 换行 ",-1))],32))}};function $f(){const e=pe(""),t=pe([]);function n(i){e.value=i}function s(){if(!e.value.trim())return;const i=e.value.trim();t.value.push({role:"user",content:i});const l=r(i),a={role:"assistant",content:"",isThinking:!0};t.value.push(a),setTimeout(()=>{const u=t.value.findIndex(f=>f.isThinking);u!==-1&&t.value.splice(u,1),t.value.push({role:"assistant",content:l||"（这里是AI助手的回复示例）"});const c=o(i);c.length>0&&setTimeout(()=>{t.value.push({role:"assistant",content:"",isFollowUp:!0,followUpQuestions:c})},800)},1500),e.value=""}function r(i){return{"房屋产权纠纷时甲方的具体赔偿范围是否明确？":'该合同虽要求甲方"确保房屋权属清晰，无产权纠纷"，但并未明确在发生产权纠纷导致乙方无法继续居住时，甲方应承担的具体赔偿责任，如退还租金、押金或补偿搬迁费用等，这在法律风险上对乙方是不利的，建议就此类情况补充详细的违约与赔偿条款，以保障租客权益。',"押金退还的房屋损耗认定标准是否有细化约定？":'合同中仅规定乙方因人为损坏需照价赔偿，但并未细化"房屋损耗"的认定标准，也未区分正常使用造成的自然损耗与人为破坏，容易在押金退还时引发争议。因此建议在签约或交房时通过拍照、视频及详细交接清单明确现状，并在合同中补充"正常使用造成的磨损不计入赔偿"的条款，以避免不必要的纠纷。',"乙方续租时租金涨幅是否受合同条款限制？":'合同仅提到乙方如需续租应提前30天书面提出，甲方"优先考虑"，但未对续租时的租金涨幅作出任何限制或说明，这意味着甲方有权在续租时大幅调整租金，对乙方不利。为保障续租权益，建议补充明确条款，如"续租租金涨幅不超过上一期租金的10%"或"在同等条件下优先续租，租金协商确定"。',"若因房屋产权问题导致租赁合同无法履行，乙方能否无责解除合同并要求赔偿？":'根据合同法相关规定，因甲方原因导致合同目的无法实现时，乙方有权解除合同并要求赔偿。本合同中甲方承诺"确保房屋权属清晰"，若违反此承诺导致合同无法履行，乙方可主张无责解除合同，并要求甲方承担违约责任，包括但不限于退还已付租金、押金、赔偿搬迁费用及相应损失。',"在产权纠纷发生期间，乙方仍继续支付租金的义务是否可以中止？":"在产权纠纷影响乙方正常使用房屋的情况下，根据合同履行中的抗辩权原则，乙方可暂停支付租金。建议合同中明确约定：当房屋因产权纠纷被查封、扣押或乙方被要求搬离时，乙方有权中止租金支付义务，直至纠纷解决且房屋可正常使用为止。","甲方在出租房屋前未主动出示房屋产权证明是否构成隐性风险？是否应作为合同附件固定？":"甲方未主动出示产权证明确实构成隐性风险，可能导致后续产权争议。建议在合同中明确要求甲方提供房屋所有权证、土地使用权证等产权证明文件，并将其复印件作为合同附件。同时约定若甲方隐瞒产权瑕疵或提供虚假证明，应承担相应法律责任和经济损失。"}[i]||null}function o(i){return{"房屋产权纠纷时甲方的具体赔偿范围是否明确？":["若因房屋产权问题导致租赁合同无法履行，乙方能否无责解除合同并要求赔偿？","在产权纠纷发生期间，乙方仍继续支付租金的义务是否可以中止？","甲方在出租房屋前未主动出示房屋产权证明是否构成隐性风险？是否应作为合同附件固定？"],"押金退还的房屋损耗认定标准是否有细化约定？":["合同未明确正常使用磨损的范围，是否可能导致甲方以轻微损耗为由扣除押金？","乙方在租期结束时如何举证自己未造成人为损坏，以确保押金顺利退还？","是否有必要在合同中列明家具电器的具体状态、估值与折旧标准，以减少争议？"],"乙方续租时租金涨幅是否受合同条款限制？":["如果甲方在续租时提出大幅涨租，乙方是否有权拒绝续租而不承担违约责任？","合同未约定续租租金标准，是否意味着甲方可单方面决定涨幅？乙方如何保障自身利益？","是否应在首次签约时约定续租租金的涨幅上限或参考市场平均涨幅？这样做有何法律效力？"]}[i]||[]}return{input:e,messages:t,selectQuestion:n,send:s}}const Mf={class:"fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col border-l border-gray-100"},Ff={class:"p-4 bg-white border-t border-gray-100"},Lf={__name:"ContractQAAssistant",props:{questions:{type:Array,default:()=>[]}},emits:["close"],setup(e,{emit:t}){const{input:n,messages:s,selectQuestion:r,send:o}=$f(),i=pe(null);vt(s,()=>{xr(()=>{l()})},{deep:!0});function l(){i.value&&(i.value.scrollTop=i.value.scrollHeight)}return(a,u)=>(M(),D("div",Mf,[Q(Zu,{onClose:u[0]||(u[0]=c=>a.$emit("close"))}),h("div",{ref_key:"messagesContainer",ref:i,class:"flex-1 overflow-y-auto px-6 py-6 bg-gray-50"},[u[2]||(u[2]=Cr('<div class="mb-6 bg-white rounded-2xl p-4 shadow-sm border border-gray-100" data-v-123c9e8a><div class="flex items-start space-x-3" data-v-123c9e8a><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1" data-v-123c9e8a><svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-123c9e8a><path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" data-v-123c9e8a></path></svg></div><div class="flex-1" data-v-123c9e8a><div class="text-gray-800 text-sm leading-relaxed" data-v-123c9e8a> 您好！我是您的合同分析助手。您可以询问关于这份合同的任何问题。 </div></div></div></div>',1)),Q(rf,{questions:e.questions,onSelect:W(r)},null,8,["questions","onSelect"]),Q(Af,{messages:W(s),onSelectQuestion:W(r)},null,8,["messages","onSelectQuestion"])],512),h("div",Ff,[Q(Pf,{modelValue:W(n),"onUpdate:modelValue":u[1]||(u[1]=c=>ve(n)?n.value=c:null),onSend:W(o)},null,8,["modelValue","onSend"])])]))}},Nf=hs(Lf,[["__scopeId","data-v-123c9e8a"]]),Gn={高:{icon:"text-red-500",text:"text-red-500"},中:{icon:"text-yellow-500",text:"text-yellow-500"},低:{icon:"text-green-500",text:"text-green-500"}},If=e=>{var t;return((t=Gn[e])==null?void 0:t.icon)||Gn.中.icon},jf=e=>{var t;return((t=Gn[e])==null?void 0:t.text)||Gn.中.text},Bf={class:"inline-flex items-baseline mb-1.5"},Mn={__name:"InfoBlock",props:{title:{type:String,required:!0},content:{type:String,required:!0},containerClass:{type:String,default:""},titleClass:{type:String,default:""},contentClass:{type:String,default:""}},setup(e){return(t,n)=>(M(),D("div",{class:ge(e.containerClass)},[h("div",Bf,[Pi(t.$slots,"icon"),h("h4",{class:ge(["text-base","font-medium",e.titleClass])},ce(e.title),3)]),h("p",{class:ge(["mt-1",e.contentClass])},ce(e.content),3)],2))}};const Df={class:"example-card border border-blue-200 rounded-lg overflow-hidden"},Uf={class:"px-6 py-4 bg-white border-t border-blue-100 divide-y divide-gray-100"},Hf={class:"pb-5"},qf={class:"prose max-w-none"},Vf={class:"text-gray-700 leading-relaxed"},zf={class:"pt-5"},Kf={class:"prose max-w-none"},Wf={class:"text-gray-700 leading-relaxed whitespace-pre-line"},Qf={__name:"CaseAnalysis",props:{exampleExplanation:{type:String,required:!0},reasoningProcess:{type:String,required:!0}},setup(e){const{isExpanded:t,toggle:n}=Or();return(s,r)=>(M(),D("div",Df,[h("div",{onClick:r[0]||(r[0]=(...o)=>W(n)&&W(n)(...o)),class:"example-card__header flex items-center justify-between px-4 py-2.5 bg-blue-600 cursor-pointer hover:bg-blue-700 transition-colors duration-200"},[r[2]||(r[2]=h("div",null,[h("div",{class:"flex items-center"},[h("svg",{class:"h-5 w-5 text-white mr-2",viewBox:"0 0 24 24",fill:"none"},[h("path",{d:"M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]),h("span",{class:"text-base font-medium text-white"},"具体案例分析")])],-1)),(M(),D("svg",{class:ge(["h-5 w-5 text-white transform transition-transform duration-200",{"rotate-180":W(t)}]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},r[1]||(r[1]=[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))]),cs(h("div",Uf,[h("div",Hf,[r[3]||(r[3]=h("div",{class:"flex items-center mb-3"},[h("svg",{class:"h-5 w-5 text-gray-600 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})]),h("h5",{class:"text-base font-medium text-gray-900"},"案例故事")],-1)),h("div",qf,[h("p",Vf,ce(e.exampleExplanation),1)])]),h("div",zf,[r[4]||(r[4]=h("div",{class:"flex items-center mb-3"},[h("svg",{class:"h-5 w-5 text-gray-600 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),h("h5",{class:"text-base font-medium text-gray-900"},"分析思路")],-1)),h("div",Kf,[h("p",Wf,ce(e.reasoningProcess),1)])])],512),[[Tr,W(t)]])]))}},Jf=hs(Qf,[["__scopeId","data-v-78402834"]]);const Gf={class:"bg-white rounded-lg shadow-lg overflow-hidden"},Xf={class:"flex items-center space-x-4"},Yf={class:"flex items-center"},Zf={class:"ml-2 text-lg font-semibold text-gray-900"},ed={class:"ml-4 flex items-center"},td={class:"px-6 py-4 space-y-4 transition-all duration-200"},nd=Object.assign({name:"RiskClauseCard"},{__name:"RiskClauseCard",props:{clause:{type:Object,required:!0},index:{type:Number,required:!0}},setup(e){const{isExpanded:t,toggle:n}=Or();return(s,r)=>(M(),D("div",Gf,[h("div",{onClick:r[0]||(r[0]=(...o)=>W(n)&&W(n)(...o)),class:"flex items-center justify-between px-6 py-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors duration-200"},[h("div",Xf,[h("div",Yf,[r[2]||(r[2]=h("span",{class:"text-lg font-semibold text-gray-900"},"条款",-1)),h("span",Zf,ce(e.index+1),1),h("div",ed,[(M(),D("svg",{class:ge(["h-5 w-5 mr-1",W(If)(e.clause.riskLevel)]),viewBox:"0 0 24 24",fill:"none"},r[1]||(r[1]=[h("path",{d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),2)),h("span",{class:ge([W(jf)(e.clause.riskLevel),"text-sm font-medium"])},ce(e.clause.riskLevel)+"级风险",3)])])]),(M(),D("svg",{class:ge(["h-6 w-6 text-gray-500 transform transition-transform duration-200",{"rotate-180":W(t)}]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},r[3]||(r[3]=[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))]),cs(h("div",td,[Q(Mn,{title:"条款内容",content:e.clause.content,"container-class":"bg-[#F8F9FC] px-4 py-2.5 rounded-lg","title-class":"text-[#334415]","content-class":"text-[#334415]"},{icon:Ut(()=>r[4]||(r[4]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-[1em] w-[1em] text-[#334415] mr-2 translate-y-[0.15em]",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}),h("path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}),h("path",{d:"M10 9H8"}),h("path",{d:"M16 13H8"}),h("path",{d:"M16 17H8"})],-1)])),_:1},8,["content"]),Q(Mn,{title:"风险说明",content:e.clause.riskDescription,"container-class":"bg-red-50 px-4 py-2.5 rounded-lg","title-class":"text-red-800","content-class":"text-red-900"},{icon:Ut(()=>r[5]||(r[5]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-[1em] w-[1em] text-red-600 mr-2 translate-y-[0.15em]",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("circle",{cx:"12",cy:"12",r:"10"}),h("line",{x1:"12",x2:"12",y1:"8",y2:"12"}),h("line",{x1:"12",x2:"12.01",y1:"16",y2:"16"})],-1)])),_:1},8,["content"]),Q(Mn,{title:"潜在后果",content:e.clause.consequences,"container-class":"bg-yellow-50 px-4 py-2.5 rounded-lg","title-class":"text-yellow-800","content-class":"text-yellow-900"},{icon:Ut(()=>r[6]||(r[6]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-[1em] w-[1em] text-yellow-600 mr-2 translate-y-[0.15em]",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("circle",{cx:"12",cy:"12",r:"10"}),h("polyline",{points:"12 6 12 12 16 14"})],-1)])),_:1},8,["content"]),Q(Mn,{title:"修改建议",content:e.clause.suggestions,"container-class":"bg-green-50 px-4 py-2.5 rounded-lg","title-class":"text-green-800","content-class":"text-green-900"},{icon:Ut(()=>r[7]||(r[7]=[h("svg",{class:"h-[1em] w-[1em] text-green-600 mr-2 translate-y-[0.15em]",viewBox:"0 0 24 24",fill:"none"},[h("path",{d:"M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),_:1},8,["content"]),Q(Jf,{"example-explanation":e.clause.exampleExplanation,"reasoning-process":e.clause.reasoningProcess},null,8,["example-explanation","reasoning-process"])],512),[[Tr,W(t)]])]))}}),sd=hs(nd,[["__scopeId","data-v-b6c0921c"]]),rd={class:"mt-8 bg-white rounded-lg shadow-lg overflow-hidden"},od={class:"p-6 space-y-4"},id={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ld={class:"bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg"},ad={class:"text-2xl font-bold text-red-900"},cd={class:"bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg md:col-span-2"},ud={class:"text-yellow-900"},fd={class:"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg"},dd={class:"text-green-900"},pd=Object.assign({name:"SummarySection"},{__name:"SummarySection",props:{summary:{type:Object,required:!0}},setup(e){return(t,n)=>(M(),D("div",rd,[n[3]||(n[3]=h("div",{class:"px-6 py-4 bg-gray-50"},[h("h3",{class:"text-xl font-semibold text-gray-900"},"总体评估")],-1)),h("div",od,[h("div",id,[h("div",ld,[n[0]||(n[0]=h("h4",{class:"text-sm font-medium text-red-800 mb-2"},"总体风险等级",-1)),h("p",ad,ce(e.summary.riskLevel),1)]),h("div",cd,[n[1]||(n[1]=h("h4",{class:"text-sm font-medium text-yellow-800 mb-2"},"主要风险点",-1)),h("p",ud,ce(e.summary.mainRisks),1)])]),h("div",fd,[n[2]||(n[2]=h("h4",{class:"text-sm font-medium text-green-800 mb-2"},"建议优先处理",-1)),h("p",dd,ce(e.summary.prioritySuggestions),1)])])]))}});function hd(e){const t=me(()=>{const s=[];try{const[r,o]=e.split("【总结】"),i=/(\d+)\.\s*条款内容：([\s\S]*?)\s*风险等级：([\s\S]*?)\s*风险说明：([\s\S]*?)\s*潜在后果：([\s\S]*?)\s*修改建议：([\s\S]*?)\s*实例解释：([\s\S]*?)\s*推理过程：([\s\S]*?)(?=\s*\d+\.\s*条款内容：|$)/g,l=r.matchAll(i);for(const a of l)s.push({number:a[1],content:a[2].trim(),riskLevel:a[3].trim(),riskDescription:a[4].trim(),consequences:a[5].trim(),suggestions:a[6].trim(),exampleExplanation:a[7].trim(),reasoningProcess:a[8].trim()});s.length===0&&console.warn("未找到任何风险条款，可能是格式不正确"),s.forEach((a,u)=>{(!a.content||!a.riskLevel||!a.riskDescription||!a.consequences||!a.suggestions||!a.exampleExplanation||!a.reasoningProcess)&&console.warn(`第${u+1}个条款的某些字段为空，可能解析不完整`)})}catch(r){console.error("解析分析结果时出错:",r)}return s}),n=me(()=>{try{const s=e.split("【总结】")[1],r=s.match(/1\.\s*总体风险等级：([\s\S]*?)(?=2\.|$)/),o=s.match(/2\.\s*主要风险点：([\s\S]*?)(?=3\.|$)/),i=s.match(/3\.\s*建议优先处理：([\s\S]*?)$/);return{riskLevel:r?r[1].trim():"未知",mainRisks:o?o[1].trim():"无",prioritySuggestions:i?i[1].trim():"无"}}catch(s){return console.error("解析总结时出错:",s),{riskLevel:"解析错误",mainRisks:"解析错误",prioritySuggestions:"解析错误"}}});return{parsedClauses:t,summary:n}}const md={class:"analysis-result"},gd={class:"space-y-6"},Xn={__name:"AnalysisResult",props:{result:{type:String,required:!0}},setup(e){const t=e,{parsedClauses:n,summary:s}=hd(t.result);return(r,o)=>(M(),D("div",md,[h("div",gd,[(M(!0),D(be,null,Cn(W(n),(i,l)=>(M(),xe(sd,{key:l,clause:i,index:l},null,8,["clause","index"]))),128))]),Q(pd,{summary:W(s)},null,8,["summary"])]))}},yd={class:"px-6 py-4"},bd={class:"border-b border-gray-200"},vd={class:"-mb-px flex space-x-8","aria-label":"Tabs"},xd={__name:"AnalysisTabs",props:{activeTab:{type:String,required:!0}},emits:["update:activeTab"],setup(e){return(t,n)=>(M(),D("div",yd,[h("div",bd,[h("nav",vd,[h("button",{onClick:n[0]||(n[0]=s=>t.$emit("update:activeTab","model_1")),class:ge(["py-4 px-1 border-b-2 font-medium text-sm",e.activeTab==="model_1"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," 模型1 分析 ",2),h("button",{onClick:n[1]||(n[1]=s=>t.$emit("update:activeTab","model_2")),class:ge(["py-4 px-1 border-b-2 font-medium text-sm",e.activeTab==="model_2"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," 模型2 分析 ",2),h("button",{onClick:n[2]||(n[2]=s=>t.$emit("update:activeTab","compare")),class:ge(["py-4 px-1 border-b-2 font-medium text-sm",e.activeTab==="compare"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," 对比视图 ",2)])])]))}},wd={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},_d={__name:"CompareView",props:{model1Result:{type:String,required:!0},model2Result:{type:String,required:!0}},setup(e){return(t,n)=>(M(),D("div",wd,[h("div",null,[n[0]||(n[0]=h("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"模型1 分析",-1)),Q(Xn,{result:e.model1Result},null,8,["result"])]),h("div",null,[n[1]||(n[1]=h("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"模型2 分析",-1)),Q(Xn,{result:e.model2Result},null,8,["result"])])]))}},Sd={class:"mt-6"},Ed={key:0,class:"prose max-w-none"},Rd={key:1,class:"prose max-w-none"},Cd={__name:"RiskAnalysisPanel",props:{analysisResult:{type:Object,required:!0},activeTab:{type:String,required:!0}},emits:["update:activeTab"],setup(e){return(t,n)=>(M(),D("div",null,[Q(xd,{"active-tab":e.activeTab,"onUpdate:activeTab":n[0]||(n[0]=s=>t.$emit("update:activeTab",s))},null,8,["active-tab"]),h("div",Sd,[e.activeTab==="model_1"?(M(),D("div",Ed,[Q(Xn,{result:e.analysisResult.model_1_result},null,8,["result"])])):Ce("",!0),e.activeTab==="model_2"?(M(),D("div",Rd,[Q(Xn,{result:e.analysisResult.model_2_result},null,8,["result"])])):Ce("",!0),e.activeTab==="compare"?(M(),xe(_d,{key:2,"model1-result":e.analysisResult.model_1_result,"model2-result":e.analysisResult.model_2_result},null,8,["model1-result","model2-result"])):Ce("",!0)])]))}};function Ad(e){const t=pe([]),n=o=>{if(!o||typeof o!="string")return[];const i=[];try{const l=o.split(/###\s+\d+\.\s+/);for(let a=1;a<l.length;a++){const u=l[a].trim();if(!u)continue;const c=u.split(`
`),f=c[0].trim();let m="",g="",v="";for(let S=1;S<c.length;S++){const w=c[S].trim();w.startsWith("**条款摘要：**")?(v="content",m=w.replace("**条款摘要：**","").trim()):w.startsWith("**通俗解释：**")?(v="interpretation",g=w.replace("**通俗解释：**","").trim()):w&&v==="content"?m+=" "+w:w&&v==="interpretation"&&(g+=" "+w)}f&&(m||g)&&i.push({number:a,title:f,content:m||"暂无摘要",interpretation:g||"暂无解释",badgeClass:s(a)})}if(i.length===0){const a=o.split(/\d+\.\s+/);for(let u=1;u<a.length;u++){const c=a[u].trim();if(!c)continue;const f=c.split(`
`).filter(m=>m.trim());if(f.length>0){const m=f[0].trim(),g=f.slice(1).join(" ").trim();i.push({number:u,title:m||`条款 ${u}`,content:g||c.substring(0,200)+"...",interpretation:"请查看完整内容了解详情",badgeClass:s(u)})}}}i.length===0&&o.trim()&&i.push({number:1,title:"合同条款分析",content:o.substring(0,500)+(o.length>500?"...":""),interpretation:"完整的条款分析内容，请查看详细信息",badgeClass:"bg-blue-100 text-blue-800"})}catch(l){console.error("解析条款分析文本时出错:",l),o.trim()&&i.push({number:1,title:"条款分析结果",content:o,interpretation:"原始分析结果",badgeClass:"bg-gray-100 text-gray-800"})}return i},s=o=>{const i=["bg-blue-100 text-blue-800","bg-green-100 text-green-800","bg-yellow-100 text-yellow-800","bg-purple-100 text-purple-800","bg-pink-100 text-pink-800","bg-indigo-100 text-indigo-800"];return i[(o-1)%i.length]};return{clauses:me(()=>n(e)),parsedClauses:t}}const Td={class:"mb-6"},Od={key:0,class:"text-center py-8"},kd={key:1,class:"text-center py-8"},Pd={key:2,class:"text-center py-8"},$d={key:0,class:"space-y-6"},Md={class:"flex items-center mb-4"},Fd={class:"text-lg font-medium text-gray-900"},Ld={class:"space-y-4"},Nd={class:"bg-gray-50 rounded-lg p-4"},Id=["innerHTML"],jd={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400"},Bd={class:"text-gray-700 leading-relaxed"},Dd=Object.assign({name:"KeyClausesPanel"},{__name:"KeyClausesPanel",props:{clausesAnalysis:{type:String,default:null},isLoading:{type:Boolean,default:!1}},setup(e){const t=e,{clauses:n}=Ad(t.clausesAnalysis),s=me(()=>!t.isLoading&&t.clausesAnalysis===null);return(r,o)=>(M(),D("div",null,[h("div",Td,[o[3]||(o[3]=h("div",{class:"flex items-center mb-4"},[h("span",{class:"mr-2 text-2xl"},"📋"),h("h3",{class:"text-xl font-semibold text-gray-900"},"条款摘要列表")],-1)),e.isLoading?(M(),D("div",Od,o[0]||(o[0]=[h("div",{class:"inline-flex items-center"},[h("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[h("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),h("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),Ot(" 正在分析合同条款... ")],-1)]))):s.value?(M(),D("div",kd,o[1]||(o[1]=[h("div",{class:"text-red-600"},[h("span",{class:"mr-2"},"❌"),Ot(" 条款分析暂时不可用，请稍后重试 ")],-1)]))):!W(n)||W(n).length===0?(M(),D("div",Pd,o[2]||(o[2]=[h("div",{class:"text-gray-500"},[h("span",{class:"mr-2"},"📄"),Ot(" 暂无条款分析结果 ")],-1)]))):Ce("",!0)]),W(n)&&W(n).length>0?(M(),D("div",$d,[(M(!0),D(be,null,Cn(W(n),i=>(M(),D("div",{key:i.number,class:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"},[h("div",Md,[h("span",{class:ge(["text-xs font-medium px-2.5 py-0.5 rounded-full mr-3",i.badgeClass])},ce(i.number),3),h("h4",Fd,ce(i.title),1)]),h("div",Ld,[h("div",Nd,[o[4]||(o[4]=h("h5",{class:"font-semibold text-gray-800 mb-2 text-base"},"条款内容",-1)),h("p",{class:"text-gray-700 leading-relaxed",innerHTML:i.content},null,8,Id)]),h("div",jd,[o[5]||(o[5]=h("h5",{class:"font-semibold text-blue-800 mb-2 flex items-center text-base"},[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"lucide lucide-book-open h-4 w-4 mr-2"},[h("path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}),h("path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"})]),Ot(" 解读 ")],-1)),h("p",Bd,ce(i.interpretation),1)])])]))),128))])):Ce("",!0)]))}}),Ud={key:0,class:"divide-y divide-gray-200"},Hd={class:"p-6"},qd={class:"flex justify-between items-center"},Vd={class:"flex space-x-3"},zd={class:"px-6 py-4 bg-gray-50"},Kd={class:"flex space-x-3"},Wd={class:"p-6"},Qd={__name:"AnalysisResultPanel",props:{analysisResult:{type:Object,required:!0}},emits:["reset-analysis","further-analysis"],setup(e,{emit:t}){const n=e,s=pe("model_1"),r=pe(!1),o=pe("risk"),i=["房屋产权纠纷时甲方的具体赔偿范围是否明确？","押金退还的房屋损耗认定标准是否有细化约定？","乙方续租时租金涨幅是否受合同条款限制？"];function l(){r.value=!0}function a(){r.value=!1}return vt(()=>n.analysisResult,()=>{s.value="model_1",o.value="risk"}),(u,c)=>{var f,m;return e.analysisResult?(M(),D("div",Ud,[h("div",Hd,[h("div",qd,[c[4]||(c[4]=h("h2",{class:"text-lg font-medium text-gray-900"},"分析结果",-1)),h("div",Vd,[h("button",{type:"button",class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:c[0]||(c[0]=g=>u.$emit("reset-analysis"))}," 重新分析 "),h("button",{type:"button",class:"inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded-md shadow-sm text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:l}," 文档对话 ")])]),Q(Xu,{duration:e.analysisResult.duration,"model-result":e.analysisResult.model_1_result},null,8,["duration","model-result"])]),h("div",zd,[h("div",Kd,[h("button",{onClick:c[1]||(c[1]=g=>o.value="risk"),class:ge(["px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",o.value==="risk"?"bg-blue-600 text-white shadow-md transform scale-105":"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400"])},c[5]||(c[5]=[h("span",{class:"mr-2"},"⚠️",-1),Ot(" 风险分析 ")]),2),h("button",{onClick:c[2]||(c[2]=g=>o.value="clauses"),class:ge(["px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",o.value==="clauses"?"bg-blue-600 text-white shadow-md transform scale-105":"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400"])},c[6]||(c[6]=[h("span",{class:"mr-2"},"📋",-1),Ot(" 条款摘要 ")]),2)])]),h("div",Wd,[o.value==="risk"?(M(),xe(Cd,{key:0,activeTab:s.value,"onUpdate:activeTab":c[3]||(c[3]=g=>s.value=g),"analysis-result":e.analysisResult},null,8,["activeTab","analysis-result"])):Ce("",!0),o.value==="clauses"?(M(),xe(Dd,{key:1,"clauses-analysis":(f=e.analysisResult)==null?void 0:f.clauses_analysis,"is-loading":!((m=e.analysisResult)!=null&&m.clauses_analysis)},null,8,["clauses-analysis","is-loading"])):Ce("",!0)]),r.value?(M(),xe(Nf,{key:0,questions:i,onClose:a})):Ce("",!0)])):Ce("",!0)}}},Jd={__name:"ResultStep",props:{analysisResult:{type:Object,required:!0}},emits:["reset-analysis","further-analysis"],setup(e){return(t,n)=>(M(),xe(Qd,{"analysis-result":e.analysisResult,onResetAnalysis:n[0]||(n[0]=s=>t.$emit("reset-analysis")),onFurtherAnalysis:n[1]||(n[1]=s=>t.$emit("further-analysis"))},null,8,["analysis-result"]))}},kr={}.VITE_API_BASE_URL||"http://localhost:8001",yo={uploading:"正在上传文件...",uploaded:"文件上传成功，正在进行AI分析...",extracting:"正在提取合同文本...",model1Analyzing:"模型1正在分析合同风险条款...",model2Analyzing:"模型2正在分析合同风险条款..."},bo={INITIAL:5,UPLOADED:15,MAX:95,COMPLETE:100};function Gd(){const e=pe(0),t=pe("正在准备分析..."),n=pe(!1),s=u=>{e.value=Math.min(Math.max(u,0),100)},r=u=>{yo[u]&&(t.value=yo[u])};return{progress:e,message:t,isProcessing:n,updateProgress:s,updateMessage:r,startProcessing:()=>{n.value=!0,e.value=bo.INITIAL,r("uploading")},completeProcessing:()=>{n.value=!1,e.value=bo.COMPLETE},resetProgress:()=>{e.value=0,t.value="正在准备分析...",n.value=!1},updateMessageByProgress:()=>{e.value<30?r("extracting"):e.value<60?r("model1Analyzing"):r("model2Analyzing")}}}function sl(e,t){return function(){return e.apply(t,arguments)}}const{toString:Xd}=Object.prototype,{getPrototypeOf:Pr}=Object,ms=(e=>t=>{const n=Xd.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Qe=e=>(e=e.toLowerCase(),t=>ms(t)===e),gs=e=>t=>typeof t===e,{isArray:Xt}=Array,_n=gs("undefined");function Yd(e){return e!==null&&!_n(e)&&e.constructor!==null&&!_n(e.constructor)&&je(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const rl=Qe("ArrayBuffer");function Zd(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&rl(e.buffer),t}const ep=gs("string"),je=gs("function"),ol=gs("number"),ys=e=>e!==null&&typeof e=="object",tp=e=>e===!0||e===!1,Bn=e=>{if(ms(e)!=="object")return!1;const t=Pr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},np=Qe("Date"),sp=Qe("File"),rp=Qe("Blob"),op=Qe("FileList"),ip=e=>ys(e)&&je(e.pipe),lp=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||je(e.append)&&((t=ms(e))==="formdata"||t==="object"&&je(e.toString)&&e.toString()==="[object FormData]"))},ap=Qe("URLSearchParams"),[cp,up,fp,dp]=["ReadableStream","Request","Response","Headers"].map(Qe),pp=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Tn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),Xt(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function il(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const kt=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),ll=e=>!_n(e)&&e!==kt;function Zs(){const{caseless:e}=ll(this)&&this||{},t={},n=(s,r)=>{const o=e&&il(t,r)||r;Bn(t[o])&&Bn(s)?t[o]=Zs(t[o],s):Bn(s)?t[o]=Zs({},s):Xt(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Tn(arguments[s],n);return t}const hp=(e,t,n,{allOwnKeys:s}={})=>(Tn(t,(r,o)=>{n&&je(r)?e[o]=sl(r,n):e[o]=r},{allOwnKeys:s}),e),mp=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),gp=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},yp=(e,t,n,s)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&Pr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},bp=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},vp=e=>{if(!e)return null;if(Xt(e))return e;let t=e.length;if(!ol(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},xp=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Pr(Uint8Array)),wp=(e,t)=>{const s=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},_p=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Sp=Qe("HTMLFormElement"),Ep=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),vo=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Rp=Qe("RegExp"),al=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Tn(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},Cp=e=>{al(e,(t,n)=>{if(je(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(je(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Ap=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return Xt(e)?s(e):s(String(e).split(t)),n},Tp=()=>{},Op=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function kp(e){return!!(e&&je(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Pp=e=>{const t=new Array(10),n=(s,r)=>{if(ys(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=Xt(s)?[]:{};return Tn(s,(i,l)=>{const a=n(i,r+1);!_n(a)&&(o[l]=a)}),t[r]=void 0,o}}return s};return n(e,0)},$p=Qe("AsyncFunction"),Mp=e=>e&&(ys(e)||je(e))&&je(e.then)&&je(e.catch),cl=((e,t)=>e?setImmediate:t?((n,s)=>(kt.addEventListener("message",({source:r,data:o})=>{r===kt&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),kt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",je(kt.postMessage)),Fp=typeof queueMicrotask<"u"?queueMicrotask.bind(kt):typeof process<"u"&&process.nextTick||cl,b={isArray:Xt,isArrayBuffer:rl,isBuffer:Yd,isFormData:lp,isArrayBufferView:Zd,isString:ep,isNumber:ol,isBoolean:tp,isObject:ys,isPlainObject:Bn,isReadableStream:cp,isRequest:up,isResponse:fp,isHeaders:dp,isUndefined:_n,isDate:np,isFile:sp,isBlob:rp,isRegExp:Rp,isFunction:je,isStream:ip,isURLSearchParams:ap,isTypedArray:xp,isFileList:op,forEach:Tn,merge:Zs,extend:hp,trim:pp,stripBOM:mp,inherits:gp,toFlatObject:yp,kindOf:ms,kindOfTest:Qe,endsWith:bp,toArray:vp,forEachEntry:wp,matchAll:_p,isHTMLForm:Sp,hasOwnProperty:vo,hasOwnProp:vo,reduceDescriptors:al,freezeMethods:Cp,toObjectSet:Ap,toCamelCase:Ep,noop:Tp,toFiniteNumber:Op,findKey:il,global:kt,isContextDefined:ll,isSpecCompliantForm:kp,toJSONObject:Pp,isAsyncFn:$p,isThenable:Mp,setImmediate:cl,asap:Fp};function z(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}b.inherits(z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const ul=z.prototype,fl={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{fl[e]={value:e}});Object.defineProperties(z,fl);Object.defineProperty(ul,"isAxiosError",{value:!0});z.from=(e,t,n,s,r,o)=>{const i=Object.create(ul);return b.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),z.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Lp=null;function er(e){return b.isPlainObject(e)||b.isArray(e)}function dl(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function xo(e,t,n){return e?e.concat(t).map(function(r,o){return r=dl(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function Np(e){return b.isArray(e)&&!e.some(er)}const Ip=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function bs(e,t,n){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=b.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,w){return!b.isUndefined(w[S])});const s=n.metaTokens,r=n.visitor||c,o=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(r))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(b.isDate(v))return v.toISOString();if(!a&&b.isBlob(v))throw new z("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(v)||b.isTypedArray(v)?a&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,S,w){let A=v;if(v&&!w&&typeof v=="object"){if(b.endsWith(S,"{}"))S=s?S:S.slice(0,-2),v=JSON.stringify(v);else if(b.isArray(v)&&Np(v)||(b.isFileList(v)||b.endsWith(S,"[]"))&&(A=b.toArray(v)))return S=dl(S),A.forEach(function(F,N){!(b.isUndefined(F)||F===null)&&t.append(i===!0?xo([S],N,o):i===null?S:S+"[]",u(F))}),!1}return er(v)?!0:(t.append(xo(w,S,o),u(v)),!1)}const f=[],m=Object.assign(Ip,{defaultVisitor:c,convertValue:u,isVisitable:er});function g(v,S){if(!b.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+S.join("."));f.push(v),b.forEach(v,function(A,k){(!(b.isUndefined(A)||A===null)&&r.call(t,A,b.isString(k)?k.trim():k,S,m))===!0&&g(A,S?S.concat(k):[k])}),f.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return g(e),t}function wo(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function $r(e,t){this._pairs=[],e&&bs(e,this,t)}const pl=$r.prototype;pl.append=function(t,n){this._pairs.push([t,n])};pl.toString=function(t){const n=t?function(s){return t.call(this,s,wo)}:wo;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function jp(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function hl(e,t,n){if(!t)return e;const s=n&&n.encode||jp;b.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=b.isURLSearchParams(t)?t.toString():new $r(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Bp{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(s){s!==null&&t(s)})}}const _o=Bp,ml={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Dp=typeof URLSearchParams<"u"?URLSearchParams:$r,Up=typeof FormData<"u"?FormData:null,Hp=typeof Blob<"u"?Blob:null,qp={isBrowser:!0,classes:{URLSearchParams:Dp,FormData:Up,Blob:Hp},protocols:["http","https","file","blob","url","data"]},Mr=typeof window<"u"&&typeof document<"u",tr=typeof navigator=="object"&&navigator||void 0,Vp=Mr&&(!tr||["ReactNative","NativeScript","NS"].indexOf(tr.product)<0),zp=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Kp=Mr&&window.location.href||"http://localhost",Wp=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Mr,hasStandardBrowserEnv:Vp,hasStandardBrowserWebWorkerEnv:zp,navigator:tr,origin:Kp},Symbol.toStringTag,{value:"Module"})),Ee={...Wp,...qp};function Qp(e,t){return bs(e,new Ee.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return Ee.isNode&&b.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Jp(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Gp(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function gl(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=n.length;return i=!i&&b.isArray(r)?r.length:i,a?(b.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!l):((!r[i]||!b.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&b.isArray(r[i])&&(r[i]=Gp(r[i])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const n={};return b.forEachEntry(e,(s,r)=>{t(Jp(s),r,n,0)}),n}return null}function Xp(e,t,n){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Fr={transitional:ml,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=b.isObject(t);if(o&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return r?JSON.stringify(gl(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Qp(t,this.formSerializer).toString();if((l=b.isFileList(t))||s.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return bs(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),Xp(t)):t}],transformResponse:[function(t){const n=this.transitional||Fr.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?z.from(l,z.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ee.classes.FormData,Blob:Ee.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{Fr.headers[e]={}});const Lr=Fr,Yp=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Zp=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&Yp[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},So=Symbol("internals");function rn(e){return e&&String(e).trim().toLowerCase()}function Dn(e){return e===!1||e==null?e:b.isArray(e)?e.map(Dn):String(e)}function eh(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const th=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ls(e,t,n,s,r){if(b.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!b.isString(t)){if(b.isString(s))return t.indexOf(s)!==-1;if(b.isRegExp(s))return s.test(t)}}function nh(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function sh(e,t){const n=b.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}class vs{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(l,a,u){const c=rn(a);if(!c)throw new Error("header name must be a non-empty string");const f=b.findKey(r,c);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||a]=Dn(l))}const i=(l,a)=>b.forEach(l,(u,c)=>o(u,c,a));if(b.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(b.isString(t)&&(t=t.trim())&&!th(t))i(Zp(t),n);else if(b.isHeaders(t))for(const[l,a]of t.entries())o(a,l,s);else t!=null&&o(n,t,s);return this}get(t,n){if(t=rn(t),t){const s=b.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return eh(r);if(b.isFunction(n))return n.call(this,r,s);if(b.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=rn(t),t){const s=b.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ls(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=rn(i),i){const l=b.findKey(s,i);l&&(!n||Ls(s,s[l],l,n))&&(delete s[l],r=!0)}}return b.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||Ls(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return b.forEach(this,(r,o)=>{const i=b.findKey(s,o);if(i){n[i]=Dn(r),delete n[o];return}const l=t?nh(o):String(o).trim();l!==o&&delete n[o],n[l]=Dn(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return b.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&b.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[So]=this[So]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=rn(i);s[l]||(sh(r,i),s[l]=!0)}return b.isArray(t)?t.forEach(o):o(t),this}}vs.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(vs.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});b.freezeMethods(vs);const Ke=vs;function Ns(e,t){const n=this||Lr,s=t||n,r=Ke.from(s.headers);let o=s.data;return b.forEach(e,function(l){o=l.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function yl(e){return!!(e&&e.__CANCEL__)}function Yt(e,t,n){z.call(this,e??"canceled",z.ERR_CANCELED,t,n),this.name="CanceledError"}b.inherits(Yt,z,{__CANCEL__:!0});function bl(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new z("Request failed with status code "+n.status,[z.ERR_BAD_REQUEST,z.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function rh(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function oh(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=s[o];i||(i=u),n[r]=a,s[r]=u;let f=o,m=0;for(;f!==r;)m+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const g=c&&u-c;return g?Math.round(m*1e3/g):void 0}}function ih(e,t){let n=0,s=1e3/t,r,o;const i=(u,c=Date.now())=>{n=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const Yn=(e,t,n=3)=>{let s=0;const r=oh(50,250);return ih(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-s,u=r(a),c=i<=l;s=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Eo=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Ro=e=>(...t)=>b.asap(()=>e(...t)),lh=Ee.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ee.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ee.origin),Ee.navigator&&/(msie|trident)/i.test(Ee.navigator.userAgent)):()=>!0,ah=Ee.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];b.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),b.isString(s)&&i.push("path="+s),b.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ch(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function uh(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function vl(e,t,n){let s=!ch(t);return e&&(s||n==!1)?uh(e,t):t}const Co=e=>e instanceof Ke?{...e}:e;function Mt(e,t){t=t||{};const n={};function s(u,c,f,m){return b.isPlainObject(u)&&b.isPlainObject(c)?b.merge.call({caseless:m},u,c):b.isPlainObject(c)?b.merge({},c):b.isArray(c)?c.slice():c}function r(u,c,f,m){if(b.isUndefined(c)){if(!b.isUndefined(u))return s(void 0,u,f,m)}else return s(u,c,f,m)}function o(u,c){if(!b.isUndefined(c))return s(void 0,c)}function i(u,c){if(b.isUndefined(c)){if(!b.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function l(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>r(Co(u),Co(c),f,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||r,m=f(e[c],t[c],c);b.isUndefined(m)&&f!==l||(n[c]=m)}),n}const xl=e=>{const t=Mt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Ke.from(i),t.url=hl(vl(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(b.isFormData(n)){if(Ee.hasStandardBrowserEnv||Ee.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ee.hasStandardBrowserEnv&&(s&&b.isFunction(s)&&(s=s(t)),s||s!==!1&&lh(t.url))){const u=r&&o&&ah.read(o);u&&i.set(r,u)}return t},fh=typeof XMLHttpRequest<"u",dh=fh&&function(e){return new Promise(function(n,s){const r=xl(e);let o=r.data;const i=Ke.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=r,c,f,m,g,v;function S(){g&&g(),v&&v(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let w=new XMLHttpRequest;w.open(r.method.toUpperCase(),r.url,!0),w.timeout=r.timeout;function A(){if(!w)return;const F=Ke.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),V={data:!l||l==="text"||l==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:F,config:e,request:w};bl(function(G){n(G),S()},function(G){s(G),S()},V),w=null}"onloadend"in w?w.onloadend=A:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(A)},w.onabort=function(){w&&(s(new z("Request aborted",z.ECONNABORTED,e,w)),w=null)},w.onerror=function(){s(new z("Network Error",z.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let N=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const V=r.transitional||ml;r.timeoutErrorMessage&&(N=r.timeoutErrorMessage),s(new z(N,V.clarifyTimeoutError?z.ETIMEDOUT:z.ECONNABORTED,e,w)),w=null},o===void 0&&i.setContentType(null),"setRequestHeader"in w&&b.forEach(i.toJSON(),function(N,V){w.setRequestHeader(V,N)}),b.isUndefined(r.withCredentials)||(w.withCredentials=!!r.withCredentials),l&&l!=="json"&&(w.responseType=r.responseType),u&&([m,v]=Yn(u,!0),w.addEventListener("progress",m)),a&&w.upload&&([f,g]=Yn(a),w.upload.addEventListener("progress",f),w.upload.addEventListener("loadend",g)),(r.cancelToken||r.signal)&&(c=F=>{w&&(s(!F||F.type?new Yt(null,e,w):F),w.abort(),w=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const k=rh(r.url);if(k&&Ee.protocols.indexOf(k)===-1){s(new z("Unsupported protocol "+k+":",z.ERR_BAD_REQUEST,e));return}w.send(o||null)})},ph=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,l();const c=u instanceof Error?u:this.reason;s.abort(c instanceof z?c:new Yt(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new z(`timeout ${t} of ms exceeded`,z.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=s;return a.unsubscribe=()=>b.asap(l),a}},hh=ph,mh=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},gh=async function*(e,t){for await(const n of yh(e))yield*mh(n,t)},yh=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Ao=(e,t,n,s)=>{const r=gh(e,t);let o=0,i,l=a=>{i||(i=!0,s&&s(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await r.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let m=o+=f;n(m)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},xs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",wl=xs&&typeof ReadableStream=="function",bh=xs&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),_l=(e,...t)=>{try{return!!e(...t)}catch{return!1}},vh=wl&&_l(()=>{let e=!1;const t=new Request(Ee.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),To=64*1024,nr=wl&&_l(()=>b.isReadableStream(new Response("").body)),Zn={stream:nr&&(e=>e.body)};xs&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Zn[t]&&(Zn[t]=b.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new z(`Response type '${t}' is not supported`,z.ERR_NOT_SUPPORT,s)})})})(new Response);const xh=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(Ee.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await bh(e)).byteLength},wh=async(e,t)=>{const n=b.toFiniteNumber(e.getContentLength());return n??xh(t)},_h=xs&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:m}=xl(e);u=u?(u+"").toLowerCase():"text";let g=hh([r,o&&o.toAbortSignal()],i),v;const S=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let w;try{if(a&&vh&&n!=="get"&&n!=="head"&&(w=await wh(c,s))!==0){let V=new Request(t,{method:"POST",body:s,duplex:"half"}),oe;if(b.isFormData(s)&&(oe=V.headers.get("content-type"))&&c.setContentType(oe),V.body){const[G,Ae]=Eo(w,Yn(Ro(a)));s=Ao(V.body,To,G,Ae)}}b.isString(f)||(f=f?"include":"omit");const A="credentials"in Request.prototype;v=new Request(t,{...m,signal:g,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:A?f:void 0});let k=await fetch(v);const F=nr&&(u==="stream"||u==="response");if(nr&&(l||F&&S)){const V={};["status","statusText","headers"].forEach(Ue=>{V[Ue]=k[Ue]});const oe=b.toFiniteNumber(k.headers.get("content-length")),[G,Ae]=l&&Eo(oe,Yn(Ro(l),!0))||[];k=new Response(Ao(k.body,To,G,()=>{Ae&&Ae(),S&&S()}),V)}u=u||"text";let N=await Zn[b.findKey(Zn,u)||"text"](k,e);return!F&&S&&S(),await new Promise((V,oe)=>{bl(V,oe,{data:N,headers:Ke.from(k.headers),status:k.status,statusText:k.statusText,config:e,request:v})})}catch(A){throw S&&S(),A&&A.name==="TypeError"&&/fetch/i.test(A.message)?Object.assign(new z("Network Error",z.ERR_NETWORK,e,v),{cause:A.cause||A}):z.from(A,A&&A.code,e,v)}}),sr={http:Lp,xhr:dh,fetch:_h};b.forEach(sr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Oo=e=>`- ${e}`,Sh=e=>b.isFunction(e)||e===null||e===!1,Sl={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!Sh(n)&&(s=sr[(i=String(n)).toLowerCase()],s===void 0))throw new z(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Oo).join(`
`):" "+Oo(o[0]):"as no adapter specified";throw new z("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:sr};function Is(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Yt(null,e)}function ko(e){return Is(e),e.headers=Ke.from(e.headers),e.data=Ns.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Sl.getAdapter(e.adapter||Lr.adapter)(e).then(function(s){return Is(e),s.data=Ns.call(e,e.transformResponse,s),s.headers=Ke.from(s.headers),s},function(s){return yl(s)||(Is(e),s&&s.response&&(s.response.data=Ns.call(e,e.transformResponse,s.response),s.response.headers=Ke.from(s.response.headers))),Promise.reject(s)})}const El="1.8.4",ws={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ws[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Po={};ws.transitional=function(t,n,s){function r(o,i){return"[Axios v"+El+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new z(r(i," has been removed"+(n?" in "+n:"")),z.ERR_DEPRECATED);return n&&!Po[i]&&(Po[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};ws.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Eh(e,t,n){if(typeof e!="object")throw new z("options must be an object",z.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new z("option "+o+" must be "+a,z.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new z("Unknown option "+o,z.ERR_BAD_OPTION)}}const Un={assertOptions:Eh,validators:ws},et=Un.validators;class es{constructor(t){this.defaults=t,this.interceptors={request:new _o,response:new _o}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Mt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&Un.assertOptions(s,{silentJSONParsing:et.transitional(et.boolean),forcedJSONParsing:et.transitional(et.boolean),clarifyTimeoutError:et.transitional(et.boolean)},!1),r!=null&&(b.isFunction(r)?n.paramsSerializer={serialize:r}:Un.assertOptions(r,{encode:et.function,serialize:et.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Un.assertOptions(n,{baseUrl:et.spelling("baseURL"),withXsrfToken:et.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&b.merge(o.common,o[n.method]);o&&b.forEach(["delete","get","head","post","put","patch","common"],v=>{delete o[v]}),n.headers=Ke.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(n)===!1||(a=a&&S.synchronous,l.unshift(S.fulfilled,S.rejected))});const u=[];this.interceptors.response.forEach(function(S){u.push(S.fulfilled,S.rejected)});let c,f=0,m;if(!a){const v=[ko.bind(this),void 0];for(v.unshift.apply(v,l),v.push.apply(v,u),m=v.length,c=Promise.resolve(n);f<m;)c=c.then(v[f++],v[f++]);return c}m=l.length;let g=n;for(f=0;f<m;){const v=l[f++],S=l[f++];try{g=v(g)}catch(w){S.call(this,w);break}}try{c=ko.call(this,g)}catch(v){return Promise.reject(v)}for(f=0,m=u.length;f<m;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=Mt(this.defaults,t);const n=vl(t.baseURL,t.url,t.allowAbsoluteUrls);return hl(n,t.params,t.paramsSerializer)}}b.forEach(["delete","get","head","options"],function(t){es.prototype[t]=function(n,s){return this.request(Mt(s||{},{method:t,url:n,data:(s||{}).data}))}});b.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(Mt(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}es.prototype[t]=n(),es.prototype[t+"Form"]=n(!0)});const Hn=es;class Nr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new Yt(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Nr(function(r){t=r}),cancel:t}}}const Rh=Nr;function Ch(e){return function(n){return e.apply(null,n)}}function Ah(e){return b.isObject(e)&&e.isAxiosError===!0}const rr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rr).forEach(([e,t])=>{rr[t]=e});const Th=rr;function Rl(e){const t=new Hn(e),n=sl(Hn.prototype.request,t);return b.extend(n,Hn.prototype,t,{allOwnKeys:!0}),b.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Rl(Mt(e,r))},n}const de=Rl(Lr);de.Axios=Hn;de.CanceledError=Yt;de.CancelToken=Rh;de.isCancel=yl;de.VERSION=El;de.toFormData=bs;de.AxiosError=z;de.Cancel=de.CanceledError;de.all=function(t){return Promise.all(t)};de.spread=Ch;de.isAxiosError=Ah;de.mergeConfig=Mt;de.AxiosHeaders=Ke;de.formToJSON=e=>gl(b.isHTMLForm(e)?new FormData(e):e);de.getAdapter=Sl.getAdapter;de.HttpStatusCode=Th;de.default=de;const Ir=de,Oh=async e=>{const t=new FormData;return t.append("file",e),(await Ir.post(`${kr}/api/upload`,t,{headers:{"Content-Type":"multipart/form-data"}})).data.task_id},kh=async e=>(await Ir.get(`${kr}/api/analysis/${e}`)).data,Ph=(e,t,n,s)=>{let r=!0;const o=async()=>{if(r)try{const i=await Ir.get(`${kr}/api/analysis/status/${e}`),{status:l,progress:a,result:u}=i.data;if(l==="completed"){n(u);return}if(l==="failed"){s("分析失败");return}t(a),setTimeout(o,2e3)}catch(i){s(i.message)}};return o(),()=>{r=!1}};function $h(){const{progress:e,message:t,isProcessing:n,updateProgress:s,updateMessage:r,startProcessing:o,completeProcessing:i,resetProgress:l}=Gd(),a=pe(null),u=pe(null),c=pe(null),f=pe(null),m=w=>{a.value=w},g=async()=>{if(a.value){o();try{const w=await Oh(a.value);c.value=w,s(15),r("uploaded"),f.value=Ph(w,A=>s(A),A=>{u.value=A,i()},A=>{alert("分析失败: "+A),n.value=!1})}catch(w){console.error("分析请求失败:",w),alert("分析请求失败，请重试"),n.value=!1}}},v=async w=>{try{const A=await kh(w);u.value=A,i()}catch(A){console.error("加载分析结果失败:",A),alert("加载分析结果失败，请重试"),n.value=!1}},S=()=>{a.value=null,u.value=null,c.value=null,l(),f.value&&(f.value(),f.value=null)};return Sr(()=>{f.value&&f.value()}),{selectedFile:a,isAnalyzing:n,analysisResult:u,analysisProgress:e,progressMessage:t,currentTaskId:c,handleFileSelected:m,startAnalysis:g,resetAnalysis:S,loadAnalysisResult:v}}/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Dt=typeof document<"u";function Cl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Mh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Cl(e.default)}const Z=Object.assign;function js(e,t){const n={};for(const s in t){const r=t[s];n[s]=We(r)?r.map(e):e(r)}return n}const hn=()=>{},We=Array.isArray,Al=/#/g,Fh=/&/g,Lh=/\//g,Nh=/=/g,Ih=/\?/g,Tl=/\+/g,jh=/%5B/g,Bh=/%5D/g,Ol=/%5E/g,Dh=/%60/g,kl=/%7B/g,Uh=/%7C/g,Pl=/%7D/g,Hh=/%20/g;function jr(e){return encodeURI(""+e).replace(Uh,"|").replace(jh,"[").replace(Bh,"]")}function qh(e){return jr(e).replace(kl,"{").replace(Pl,"}").replace(Ol,"^")}function or(e){return jr(e).replace(Tl,"%2B").replace(Hh,"+").replace(Al,"%23").replace(Fh,"%26").replace(Dh,"`").replace(kl,"{").replace(Pl,"}").replace(Ol,"^")}function Vh(e){return or(e).replace(Nh,"%3D")}function zh(e){return jr(e).replace(Al,"%23").replace(Ih,"%3F")}function Kh(e){return e==null?"":zh(e).replace(Lh,"%2F")}function Sn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Wh=/\/$/,Qh=e=>e.replace(Wh,"");function Bs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Yh(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Sn(i)}}function Jh(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function $o(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Gh(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Jt(t.matched[s],n.matched[r])&&$l(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Jt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function $l(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Xh(e[n],t[n]))return!1;return!0}function Xh(e,t){return We(e)?Mo(e,t):We(t)?Mo(t,e):e===t}function Mo(e,t){return We(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Yh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const ht={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var En;(function(e){e.pop="pop",e.push="push"})(En||(En={}));var mn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(mn||(mn={}));function Zh(e){if(!e)if(Dt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Qh(e)}const em=/^[^#]+#/;function tm(e,t){return e.replace(em,"#")+t}function nm(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const _s=()=>({left:window.scrollX,top:window.scrollY});function sm(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=nm(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Fo(e,t){return(history.state?history.state.position-t:-1)+e}const ir=new Map;function rm(e,t){ir.set(e,t)}function om(e){const t=ir.get(e);return ir.delete(e),t}let im=()=>location.protocol+"//"+location.host;function Ml(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),$o(a,"")}return $o(n,e)+s+r}function lm(e,t,n,s){let r=[],o=[],i=null;const l=({state:m})=>{const g=Ml(e,location),v=n.value,S=t.value;let w=0;if(m){if(n.value=g,t.value=m,i&&i===v){i=null;return}w=S?m.position-S.position:0}else s(g);r.forEach(A=>{A(n.value,v,{delta:w,type:En.pop,direction:w?w>0?mn.forward:mn.back:mn.unknown})})};function a(){i=n.value}function u(m){r.push(m);const g=()=>{const v=r.indexOf(m);v>-1&&r.splice(v,1)};return o.push(g),g}function c(){const{history:m}=window;m.state&&m.replaceState(Z({},m.state,{scroll:_s()}),"")}function f(){for(const m of o)m();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function Lo(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?_s():null}}function am(e){const{history:t,location:n}=window,s={value:Ml(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),m=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:im()+e+a;try{t[c?"replaceState":"pushState"](u,"",m),r.value=u}catch(g){console.error(g),n[c?"replace":"assign"](m)}}function i(a,u){const c=Z({},t.state,Lo(r.value.back,a,r.value.forward,!0),u,{position:r.value.position});o(a,c,!0),s.value=a}function l(a,u){const c=Z({},r.value,t.state,{forward:a,scroll:_s()});o(c.current,c,!0);const f=Z({},Lo(s.value,a,null),{position:c.position+1},u);o(a,f,!1),s.value=a}return{location:s,state:r,push:l,replace:i}}function cm(e){e=Zh(e);const t=am(e),n=lm(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Z({location:"",base:e,go:s,createHref:tm.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function um(e){return typeof e=="string"||e&&typeof e=="object"}function Fl(e){return typeof e=="string"||typeof e=="symbol"}const Ll=Symbol("");var No;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(No||(No={}));function Gt(e,t){return Z(new Error,{type:e,[Ll]:!0},t)}function it(e,t){return e instanceof Error&&Ll in e&&(t==null||!!(e.type&t))}const Io="[^/]+?",fm={sensitive:!1,strict:!1,start:!0,end:!0},dm=/[.+*?^${}()[\]/\\]/g;function pm(e,t){const n=Z({},fm,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const m=u[f];let g=40+(n.sensitive?.25:0);if(m.type===0)f||(r+="/"),r+=m.value.replace(dm,"\\$&"),g+=40;else if(m.type===1){const{value:v,repeatable:S,optional:w,regexp:A}=m;o.push({name:v,repeatable:S,optional:w});const k=A||Io;if(k!==Io){g+=10;try{new RegExp(`(${k})`)}catch(N){throw new Error(`Invalid custom RegExp for param "${v}" (${k}): `+N.message)}}let F=S?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;f||(F=w&&u.length<2?`(?:/${F})`:"/"+F),w&&(F+="?"),r+=F,g+=20,w&&(g+=-8),S&&(g+=-20),k===".*"&&(g+=-50)}c.push(g)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let m=1;m<c.length;m++){const g=c[m]||"",v=o[m-1];f[v.name]=g&&v.repeatable?g.split("/"):g}return f}function a(u){let c="",f=!1;for(const m of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const g of m)if(g.type===0)c+=g.value;else if(g.type===1){const{value:v,repeatable:S,optional:w}=g,A=v in u?u[v]:"";if(We(A)&&!S)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const k=We(A)?A.join("/"):A;if(!k)if(w)m.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${v}"`);c+=k}}return c||"/"}return{re:i,score:s,keys:o,parse:l,stringify:a}}function hm(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Nl(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=hm(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(jo(s))return 1;if(jo(r))return-1}return r.length-s.length}function jo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const mm={type:0,value:""},gm=/[a-zA-Z0-9_]/;function ym(e){if(!e)return[[]];if(e==="/")return[[mm]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function m(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):m();break;case 4:m(),n=s;break;case 1:a==="("?n=2:gm.test(a)?m():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function bm(e,t,n){const s=pm(ym(e.path),n),r=Z(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function vm(e,t){const n=[],s=new Map;t=Ho({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,m,g){const v=!g,S=Do(f);S.aliasOf=g&&g.record;const w=Ho(t,f),A=[S];if("alias"in f){const N=typeof f.alias=="string"?[f.alias]:f.alias;for(const V of N)A.push(Do(Z({},S,{components:g?g.record.components:S.components,path:V,aliasOf:g?g.record:S})))}let k,F;for(const N of A){const{path:V}=N;if(m&&V[0]!=="/"){const oe=m.record.path,G=oe[oe.length-1]==="/"?"":"/";N.path=m.record.path+(V&&G+V)}if(k=bm(N,m,w),g?g.alias.push(k):(F=F||k,F!==k&&F.alias.push(k),v&&f.name&&!Uo(k)&&i(f.name)),Il(k)&&a(k),S.children){const oe=S.children;for(let G=0;G<oe.length;G++)o(oe[G],k,g&&g.children[G])}g=g||k}return F?()=>{i(F)}:hn}function i(f){if(Fl(f)){const m=s.get(f);m&&(s.delete(f),n.splice(n.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=n.indexOf(f);m>-1&&(n.splice(m,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const m=_m(f,n);n.splice(m,0,f),f.record.name&&!Uo(f)&&s.set(f.record.name,f)}function u(f,m){let g,v={},S,w;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw Gt(1,{location:f});w=g.record.name,v=Z(Bo(m.params,g.keys.filter(F=>!F.optional).concat(g.parent?g.parent.keys.filter(F=>F.optional):[]).map(F=>F.name)),f.params&&Bo(f.params,g.keys.map(F=>F.name))),S=g.stringify(v)}else if(f.path!=null)S=f.path,g=n.find(F=>F.re.test(S)),g&&(v=g.parse(S),w=g.record.name);else{if(g=m.name?s.get(m.name):n.find(F=>F.re.test(m.path)),!g)throw Gt(1,{location:f,currentLocation:m});w=g.record.name,v=Z({},m.params,f.params),S=g.stringify(v)}const A=[];let k=g;for(;k;)A.unshift(k.record),k=k.parent;return{name:w,path:S,params:v,matched:A,meta:wm(A)}}e.forEach(f=>o(f));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function Bo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Do(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:xm(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function xm(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Uo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function wm(e){return e.reduce((t,n)=>Z(t,n.meta),{})}function Ho(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function _m(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Nl(e,t[o])<0?s=o:n=o+1}const r=Sm(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Sm(e){let t=e;for(;t=t.parent;)if(Il(t)&&Nl(e,t)===0)return t}function Il({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Em(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Tl," "),i=o.indexOf("="),l=Sn(i<0?o:o.slice(0,i)),a=i<0?null:Sn(o.slice(i+1));if(l in t){let u=t[l];We(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function qo(e){let t="";for(let n in e){const s=e[n];if(n=Vh(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(We(s)?s.map(o=>o&&or(o)):[s&&or(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Rm(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=We(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Cm=Symbol(""),Vo=Symbol(""),Ss=Symbol(""),Br=Symbol(""),lr=Symbol("");function on(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function yt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const u=m=>{m===!1?a(Gt(4,{from:n,to:t})):m instanceof Error?a(m):um(m)?a(Gt(2,{from:t,to:m})):(i&&s.enterCallbacks[r]===i&&typeof m=="function"&&i.push(m),l())},c=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(m=>a(m))})}function Ds(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Cl(a)){const c=(a.__vccOpts||a)[t];c&&o.push(yt(c,n,s,i,l,r))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=Mh(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const g=(f.__vccOpts||f)[t];return g&&yt(g,n,s,i,l,r)()}))}}return o}function zo(e){const t=ze(Ss),n=ze(Br),s=me(()=>{const a=W(e.to);return t.resolve(a)}),r=me(()=>{const{matched:a}=s.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const m=f.findIndex(Jt.bind(null,c));if(m>-1)return m;const g=Ko(a[u-2]);return u>1&&Ko(c)===g&&f[f.length-1].path!==g?f.findIndex(Jt.bind(null,a[u-2])):m}),o=me(()=>r.value>-1&&Pm(n.params,s.value.params)),i=me(()=>r.value>-1&&r.value===n.matched.length-1&&$l(n.params,s.value.params));function l(a={}){if(km(a)){const u=t[W(e.replace)?"replace":"push"](W(e.to)).catch(hn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:me(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function Am(e){return e.length===1?e[0]:e}const Tm=Ai({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:zo,setup(e,{slots:t}){const n=ls(zo(e)),{options:s}=ze(Ss),r=me(()=>({[Wo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Wo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Am(t.default(n));return e.custom?o:el("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Om=Tm;function km(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pm(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!We(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Ko(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Wo=(e,t,n)=>e??t??n,$m=Ai({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=ze(lr),r=me(()=>e.route||s.value),o=ze(Vo,0),i=me(()=>{let u=W(o);const{matched:c}=r.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=me(()=>r.value.matched[i.value]);Ln(Vo,me(()=>i.value+1)),Ln(Cm,l),Ln(lr,r);const a=pe();return vt(()=>[a.value,l.value,e.name],([u,c,f],[m,g,v])=>{c&&(c.instances[f]=u,g&&g!==c&&u&&u===m&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),u&&c&&(!g||!Jt(c,g)||!m)&&(c.enterCallbacks[f]||[]).forEach(S=>S(u))},{flush:"post"}),()=>{const u=r.value,c=e.name,f=l.value,m=f&&f.components[c];if(!m)return Qo(n.default,{Component:m,route:u});const g=f.props[c],v=g?g===!0?u.params:typeof g=="function"?g(u):g:null,w=el(m,Z({},v,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return Qo(n.default,{Component:w,route:u})||w}}});function Qo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Mm=$m;function Fm(e){const t=vm(e.routes,e),n=e.parseQuery||Em,s=e.stringifyQuery||qo,r=e.history,o=on(),i=on(),l=on(),a=ya(ht);let u=ht;Dt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=js.bind(null,_=>""+_),f=js.bind(null,Kh),m=js.bind(null,Sn);function g(_,I){let $,j;return Fl(_)?($=t.getRecordMatcher(_),j=I):j=_,t.addRoute(j,$)}function v(_){const I=t.getRecordMatcher(_);I&&t.removeRoute(I)}function S(){return t.getRoutes().map(_=>_.record)}function w(_){return!!t.getRecordMatcher(_)}function A(_,I){if(I=Z({},I||a.value),typeof _=="string"){const p=Bs(n,_,I.path),y=t.resolve({path:p.path},I),E=r.createHref(p.fullPath);return Z(p,y,{params:m(y.params),hash:Sn(p.hash),redirectedFrom:void 0,href:E})}let $;if(_.path!=null)$=Z({},_,{path:Bs(n,_.path,I.path).path});else{const p=Z({},_.params);for(const y in p)p[y]==null&&delete p[y];$=Z({},_,{params:f(p)}),I.params=f(I.params)}const j=t.resolve($,I),Y=_.hash||"";j.params=c(m(j.params));const ae=Jh(s,Z({},_,{hash:qh(Y),path:j.path})),d=r.createHref(ae);return Z({fullPath:ae,hash:Y,query:s===qo?Rm(_.query):_.query||{}},j,{redirectedFrom:void 0,href:d})}function k(_){return typeof _=="string"?Bs(n,_,a.value.path):Z({},_)}function F(_,I){if(u!==_)return Gt(8,{from:I,to:_})}function N(_){return G(_)}function V(_){return N(Z(k(_),{replace:!0}))}function oe(_){const I=_.matched[_.matched.length-1];if(I&&I.redirect){const{redirect:$}=I;let j=typeof $=="function"?$(_):$;return typeof j=="string"&&(j=j.includes("?")||j.includes("#")?j=k(j):{path:j},j.params={}),Z({query:_.query,hash:_.hash,params:j.path!=null?{}:_.params},j)}}function G(_,I){const $=u=A(_),j=a.value,Y=_.state,ae=_.force,d=_.replace===!0,p=oe($);if(p)return G(Z(k(p),{state:typeof p=="object"?Z({},Y,p.state):Y,force:ae,replace:d}),I||$);const y=$;y.redirectedFrom=I;let E;return!ae&&Gh(s,j,$)&&(E=Gt(16,{to:y,from:j}),Xe(j,j,!0,!1)),(E?Promise.resolve(E):Je(y,j)).catch(x=>it(x)?it(x,2)?x:pt(x):X(x,y,j)).then(x=>{if(x){if(it(x,2))return G(Z({replace:d},k(x.to),{state:typeof x.to=="object"?Z({},Y,x.to.state):Y,force:ae}),I||y)}else x=Rt(y,j,!0,d,Y);return dt(y,j,x),x})}function Ae(_,I){const $=F(_,I);return $?Promise.reject($):Promise.resolve()}function Ue(_){const I=Nt.values().next().value;return I&&typeof I.runWithContext=="function"?I.runWithContext(_):_()}function Je(_,I){let $;const[j,Y,ae]=Lm(_,I);$=Ds(j.reverse(),"beforeRouteLeave",_,I);for(const p of j)p.leaveGuards.forEach(y=>{$.push(yt(y,_,I))});const d=Ae.bind(null,_,I);return $.push(d),Be($).then(()=>{$=[];for(const p of o.list())$.push(yt(p,_,I));return $.push(d),Be($)}).then(()=>{$=Ds(Y,"beforeRouteUpdate",_,I);for(const p of Y)p.updateGuards.forEach(y=>{$.push(yt(y,_,I))});return $.push(d),Be($)}).then(()=>{$=[];for(const p of ae)if(p.beforeEnter)if(We(p.beforeEnter))for(const y of p.beforeEnter)$.push(yt(y,_,I));else $.push(yt(p.beforeEnter,_,I));return $.push(d),Be($)}).then(()=>(_.matched.forEach(p=>p.enterCallbacks={}),$=Ds(ae,"beforeRouteEnter",_,I,Ue),$.push(d),Be($))).then(()=>{$=[];for(const p of i.list())$.push(yt(p,_,I));return $.push(d),Be($)}).catch(p=>it(p,8)?p:Promise.reject(p))}function dt(_,I,$){l.list().forEach(j=>Ue(()=>j(_,I,$)))}function Rt(_,I,$,j,Y){const ae=F(_,I);if(ae)return ae;const d=I===ht,p=Dt?history.state:{};$&&(j||d?r.replace(_.fullPath,Z({scroll:d&&p&&p.scroll},Y)):r.push(_.fullPath,Y)),a.value=_,Xe(_,I,$,d),pt()}let Ge;function Zt(){Ge||(Ge=r.listen((_,I,$)=>{if(!On.listening)return;const j=A(_),Y=oe(j);if(Y){G(Z(Y,{replace:!0,force:!0}),j).catch(hn);return}u=j;const ae=a.value;Dt&&rm(Fo(ae.fullPath,$.delta),_s()),Je(j,ae).catch(d=>it(d,12)?d:it(d,2)?(G(Z(k(d.to),{force:!0}),j).then(p=>{it(p,20)&&!$.delta&&$.type===En.pop&&r.go(-1,!1)}).catch(hn),Promise.reject()):($.delta&&r.go(-$.delta,!1),X(d,j,ae))).then(d=>{d=d||Rt(j,ae,!1),d&&($.delta&&!it(d,8)?r.go(-$.delta,!1):$.type===En.pop&&it(d,20)&&r.go(-1,!1)),dt(j,ae,d)}).catch(hn)}))}let Ft=on(),he=on(),se;function X(_,I,$){pt(_);const j=he.list();return j.length?j.forEach(Y=>Y(_,I,$)):console.error(_),Promise.reject(_)}function rt(){return se&&a.value!==ht?Promise.resolve():new Promise((_,I)=>{Ft.add([_,I])})}function pt(_){return se||(se=!_,Zt(),Ft.list().forEach(([I,$])=>_?$(_):I()),Ft.reset()),_}function Xe(_,I,$,j){const{scrollBehavior:Y}=e;if(!Dt||!Y)return Promise.resolve();const ae=!$&&om(Fo(_.fullPath,0))||(j||!$)&&history.state&&history.state.scroll||null;return xr().then(()=>Y(_,I,ae)).then(d=>d&&sm(d)).catch(d=>X(d,_,I))}const Pe=_=>r.go(_);let Lt;const Nt=new Set,On={currentRoute:a,listening:!0,addRoute:g,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:S,resolve:A,options:e,push:N,replace:V,go:Pe,back:()=>Pe(-1),forward:()=>Pe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:he.add,isReady:rt,install(_){const I=this;_.component("RouterLink",Om),_.component("RouterView",Mm),_.config.globalProperties.$router=I,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>W(a)}),Dt&&!Lt&&a.value===ht&&(Lt=!0,N(r.location).catch(Y=>{}));const $={};for(const Y in ht)Object.defineProperty($,Y,{get:()=>a.value[Y],enumerable:!0});_.provide(Ss,I),_.provide(Br,bi($)),_.provide(lr,a);const j=_.unmount;Nt.add(_),_.unmount=function(){Nt.delete(_),Nt.size<1&&(u=ht,Ge&&Ge(),Ge=null,a.value=ht,Lt=!1,se=!1),j()}}};function Be(_){return _.reduce((I,$)=>I.then(()=>Ue($)),Promise.resolve())}return On}function Lm(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>Jt(u,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>Jt(u,a))||r.push(a))}return[n,s,r]}function Nm(){return ze(Ss)}function Im(e){return ze(Br)}function jm(e,t){const n=Im(),s=Nm();return vt(e,r=>{r!=null&&r.id&&s.push(`/analysis/${r.id}`)}),vt(()=>n.params.id,async r=>{r&&(!e.value||e.value.id!==r)&&await t(r)},{immediate:!0}),{route:n,router:s}}const Bm={__name:"AnalysisWorkflow",setup(e){const{isAnalyzing:t,analysisResult:n,analysisProgress:s,progressMessage:r,handleFileSelected:o,startAnalysis:i,resetAnalysis:l,loadAnalysisResult:a}=$h();jm(n,a);const u=me(()=>n.value?"result":t.value?"analyzing":"upload"),c=()=>{window.alert("功能开发中")};return(f,m)=>(M(),D("div",null,[u.value==="upload"?(M(),xe(vu,{key:0,onFileSelected:W(o),onStartAnalysis:W(i)},null,8,["onFileSelected","onStartAnalysis"])):Ce("",!0),u.value==="analyzing"?(M(),xe(Ru,{key:1,"is-analyzing":W(t),"progress-message":W(r),"analysis-progress":W(s)},null,8,["is-analyzing","progress-message","analysis-progress"])):Ce("",!0),u.value==="result"?(M(),xe(Jd,{key:2,"analysis-result":W(n),onResetAnalysis:W(l),onFurtherAnalysis:c},null,8,["analysis-result","onResetAnalysis"])):Ce("",!0)]))}};const Dm={class:"min-h-screen bg-gray-50"},ar={__name:"App",setup(e){return(t,n)=>(M(),D("div",Dm,[Q(fu),Q(hu,null,{default:Ut(()=>[Q(Bm)]),_:1}),Q(lu)]))}},Um=[{path:"/",name:"Home",component:ar,props:!0},{path:"/analysis/:id",name:"AnalysisResult",component:ar,props:!0}],Hm=Fm({history:cm(),routes:Um});const jl=tu(ar);jl.use(Hm);jl.mount("#app");
