<template>
  <div>
    <div class="mb-6">
      <div class="flex items-center mb-4">
        <span class="mr-2 text-2xl">📋</span>
        <h3 class="text-xl font-semibold text-gray-900">条款摘要列表</h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="text-center py-8">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在分析合同条款...
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="text-center py-8">
        <div class="text-red-600">
          <span class="mr-2">❌</span>
          条款分析暂时不可用，请稍后重试
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!clauses || clauses.length === 0" class="text-center py-8">
        <div class="text-gray-500">
          <span class="mr-2">📄</span>
          暂无条款分析结果
        </div>
      </div>
    </div>

    <!-- 条款列表 -->
    <div v-if="clauses && clauses.length > 0" class="space-y-6">
      <div
        v-for="clause in clauses"
        :key="clause.number"
        class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
      >
        <div class="flex items-center mb-4">
          <span
            class="text-xs font-medium px-2.5 py-0.5 rounded-full mr-3"
            :class="clause.badgeClass"
          >
            {{ clause.number }}
          </span>
          <h4 class="text-lg font-medium text-gray-900">{{ clause.title }}</h4>
        </div>
        
        <div class="space-y-4">
          <!-- 条款内容 Block -->
          <div class="bg-gray-50 rounded-lg p-4">
            <h5 class="font-semibold text-gray-800 mb-2 text-base">条款内容</h5>
            <p class="text-gray-700 leading-relaxed" v-html="clause.content"></p>
          </div>

          <!-- 解读 Block -->
          <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
            <h5 class="font-semibold text-blue-800 mb-2 flex items-center text-base">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-4 w-4 mr-2">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
              解读
            </h5>
            <p class="text-gray-700 leading-relaxed">{{ clause.interpretation }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useClausesParser } from './composables/useClausesParser'

defineOptions({
  name: 'KeyClausesPanel'
})

const props = defineProps({
  clausesAnalysis: {
    type: String,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

// 使用条款解析器
const { clauses } = useClausesParser(props.clausesAnalysis)

// 计算是否有错误
const hasError = computed(() => {
  return !props.isLoading && props.clausesAnalysis === null
})


</script> 