<template>
  <div>
    <div class="mb-6">
      <div class="flex items-center mb-4">
        <span class="mr-2 text-2xl">📋</span>
        <h3 class="text-xl font-semibold text-gray-900">条款摘要列表</h3>
      </div>
    </div>

    <div class="space-y-6">
      <div
        v-for="clause in clauses"
        :key="clause.number"
        class="bg-white border border-gray-200 rounded-lg p-6"
      >
        <div class="flex items-center mb-4">
          <span 
            class="text-xs font-medium px-2.5 py-0.5 rounded-full mr-3"
            :class="clause.badgeClass"
          >
            {{ clause.number }}
          </span>
          <h4 class="text-lg font-medium text-gray-900">{{ clause.title }}</h4>
        </div>
        
        <div class="space-y-4">
          <!-- 条款内容 Block -->
          <div class="bg-gray-50 rounded-lg p-4">
            <h5 class="font-semibold text-gray-800 mb-2 text-base">条款内容</h5>
            <p class="text-gray-700 leading-relaxed" v-html="clause.content"></p>
          </div>

          <!-- 解读 Block -->
          <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
            <h5 class="font-semibold text-blue-800 mb-2 flex items-center text-base">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-4 w-4 mr-2">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
              解读
            </h5>
            <p class="text-gray-700 leading-relaxed">{{ clause.interpretation }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'KeyClausesPanel'
})

const clauses = ref([
  {
    number: 1,
    title: '第一条 房屋基本情况',
    content: '甲方出租位于上海市长宁区中山西路 500 弄 3 号 601 室、建筑面积 85 ㎡的精装修两室一厅（含全套家电家具），仅作居住使用。',
    interpretation: '房东把一套拎包入住的85平两居室租给你，只能当住宅用，不能拿去做生意。',
    badgeClass: 'bg-blue-100 text-blue-800'
  },
  {
    number: 2,
    title: '第二条 租赁期限',
    content: '租期 2 年：自 2025-04-10 至 2027-04-09。',
    interpretation: '这份合同锁定两年，中途到期日期写得很清楚。',
    badgeClass: 'bg-green-100 text-green-800'
  },
  {
    number: 3,
    title: '第三条 租金及支付方式',
    content: '月租 7 200 元，按季度一次付三个月（21 600 元），应在每季度首月 5 日前转账；逾期每日加收 1‰ 违约金。',
    interpretation: '房租一次付三个月，晚一天就要按千分之一交"滞纳金"。',
    badgeClass: 'bg-yellow-100 text-yellow-800'
  },
  {
    number: 4,
    title: '第四条 押金',
    content: '签约时支付两个月租金共 14 400 元，退租验房无损坏后无息返还。',
    interpretation: '押两付零，搬走时房子完好就能全额拿回，不生利息。',
    badgeClass: 'bg-purple-100 text-purple-800'
  },
  {
    number: 5,
    title: '第五条 房屋用途及限制',
    content: '仅限合法居住；未经书面同意，不得改作商业用途、转租或转借。',
    interpretation: '只能自己住，不能私下分租、做工作室或改造房子。',
    badgeClass: 'bg-red-100 text-red-800'
  },
  {
    number: 6,
    title: '第六条 双方权利与义务',
    content: '• 甲方：保证产权清晰、设施完好，非约定不得提前收回。<br>• 乙方：爱护房屋及设备，自付水电网物业费，不得擅改结构或装修。',
    interpretation: '房东负责提供合法、安全的房子；租客负责好好用、损坏照赔，并自己承担日常费用。',
    badgeClass: 'bg-indigo-100 text-indigo-800'
  },
  {
    number: 7,
    title: '第七条 违约责任',
    content: '乙方无故提前退租须赔付一个月租金；甲方中途毁约需退全押金并赔乙方一个月租金。',
    interpretation: '谁先毁约谁赔钱，金额都定好了，一目了然。',
    badgeClass: 'bg-orange-100 text-orange-800'
  },
  {
    number: 8,
    title: '第八条 房屋交接',
    content: '双方 2025-04-10 现场点交并签《房屋交接单》，确认家具电器完好。',
    interpretation: '交钥匙当天双方一起验房、清点设备，白纸黑字留存。',
    badgeClass: 'bg-teal-100 text-teal-800'
  },
  {
    number: 9,
    title: '第九条 不可抗力',
    content: '地震、洪水、火灾等致房屋无法使用，合同自动终止，双方互不追责。',
    interpretation: '天灾毁房子，合同自然作废，谁都不用赔谁。',
    badgeClass: 'bg-gray-100 text-gray-800'
  },
  {
    number: 10,
    title: '第十条 其他约定',
    content: '乙方可自费加装安保设备需书面同意；扰民屡犯视为违约；续租须提前 30 天书面提出。',
    interpretation: '加装门锁监控要先征求房东；别总被邻居投诉；想续租得提前打招呼。',
    badgeClass: 'bg-pink-100 text-pink-800'
  },
  {
    number: 11,
    title: '第十一条 合同解除',
    content: '甲方可在乙方拖欠租金超 30 天、转租、违法、拆迁等情形下单方面解除合同。',
    interpretation: '若租客欠费或干坏事，或房子被拆迁，房东有权直接结束合同让你搬。',
    badgeClass: 'bg-red-100 text-red-800'
  },
  {
    number: 12,
    title: '第十二条 合同争议处理',
    content: '协商不成，向房屋所在地人民法院起诉。',
    interpretation: '真闹到打官司，按房子所在区法院管辖。',
    badgeClass: 'bg-cyan-100 text-cyan-800'
  },
  {
    number: 13,
    title: '第十三条 合同份数与生效',
    content: '一式两份，双方各执一份，自签字之日生效。',
    interpretation: '合同各自保留一份，签完当天就算正式生效。',
    badgeClass: 'bg-emerald-100 text-emerald-800'
  }
])
</script> 