from openai import OpenAI
import logging
from config import MODEL_1, MODEL_2

logger = logging.getLogger(__name__)

# 常量定义
ROLE_PROMPT = "你是一位专业的法律顾问，专门负责分析合同中的风险条款。请以严谨、专业的态度进行分析，并严格按照指定的格式输出结果，不得出现任何其他额外的内容。请确保每个字段的内容完整，不被截断。"

EXAMPLE_EXPLANATION_PROMPT = """请生成一个300字以内的简短故事，用通俗易懂的语言，帮助人们理解该条款可能带来的法律风险。
故事应设定贴近真实情境，并包含具体人物或公司，以增强代入感。
在故事中需明确具体条款的主要内容，并展现该条款所带来的实际影响；通过情节推进，揭示条款在现实操作中可能引发的困扰、误解或实际损失。
建议尽可能使用具体数字佐证，如"损失了500元押金"或"被额外扣款200元"，以提升说服力。
故事结尾应以通俗建议的方式提出具体且详细的改进意见，例如"如果平台能提前告知清楚费用明细，小张就不会吃亏了"，引导读者理解规则应如何优化。
整体表达应避免使用法律术语，文字风格也要比较专业精准、有画面感，避免空泛说教，用紧凑且富有起伏的情节引发共鸣，使内容更具警示性和科普价值。"""

ACTION_PROMPT_TEMPLATE = """
请仔细阅读以下房屋租赁合同，并分析其中可能存在的高风险条款。
语言专业但通俗易懂，避免使用过于法律化或口语化的表达。
推理过程需逐步列出，结构清晰，便于理解，逻辑完整；步骤数量视条款复杂程度灵活增减。

请严格按照以下格式输出分析结果，注意：
1. 每个字段必须完整，不能省略
2. 每个字段之间使用换行分隔
3. 确保实例解释部分完整，不被截断
4. 严格遵守标点符号和换行要求

【高风险条款分析】

1. 条款内容：[具体条款内容]

   风险等级：[高/中/低]

   风险说明：[详细解释为什么这是高风险条款]

   潜在后果：[可能带来的具体风险或后果]

   修改建议：[具体的修改或协商建议]

   实例解释：[{example_explanation_prompt}]

   推理过程：[请用一段或两段文字，清晰展示你是如何一步步分析并得出该结论的。分析过程应详实具体，逻辑严密、条理清晰，能体现出你对关键要素的识别、判断依据的推理过程，以及如何从事实出发逐步得出最终结论的整体思路。务必做到内容可理解、思路连贯，方便他人跟随你的推理路径。]
   
2. 条款内容：[具体条款内容]

   风险等级：[高/中/低]

   风险说明：[详细解释为什么这是高风险条款]

   潜在后果：[可能带来的具体风险或后果]

   修改建议：[具体的修改或协商建议]

   实例解释：[{example_explanation_prompt}]

   推理过程：[请用一段或两段文字，清晰展示你是如何一步步分析并得出该结论的。分析过程应详实具体，逻辑严密、条理清晰，能体现出你对关键要素的识别、判断依据的推理过程，以及如何从事实出发逐步得出最终结论的整体思路。务必做到内容可理解、思路连贯，方便他人跟随你的推理路径。]

[以此类推...]

【总结】
1. 总体风险等级：[高/中/低]
2. 主要风险点：[简要总结主要风险]
3. 建议优先处理：[建议优先处理的风险条款]

合同内容如下：

{text}
"""

# 合同条款分析相关的提示词模板
CLAUSE_ANALYSIS_PROMPT_TEMPLATE = """请详细分析以下合同文档，并按照以下格式输出分析结果：

合同文档内容：
{text}

请按照以下结构进行分析：

## 主要条款摘要

### 1. [条款名称]
**条款摘要：** [简要说明该条款的主要内容]
**通俗解释：** [用通俗易懂的语言解释该条款的含义和影响]

### 2. [条款名称]
**条款摘要：** [简要说明该条款的主要内容]
**通俗解释：** [用通俗易懂的语言解释该条款的含义和影响]

[继续列出所有识别出的条款...]

请确保：
1. 识别出合同中的所有重要条款
2. 每个条款都要有清晰的摘要和通俗解释
3. 使用通俗易懂的语言进行解释"""

# 初始化AI客户端
def get_doubao_client():
    return OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key="a06938e5-4da1-41eb-9dc1-4e7f7bd7f9ed"
    )

def get_openrouter_client():
    return OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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"
    )

# 使用豆包AI分析
async def analyze_with_model_1(text):
    try:
        client = get_openrouter_client()
        action_prompt = ACTION_PROMPT_TEMPLATE.format(
            text=text,
            example_explanation_prompt=EXAMPLE_EXPLANATION_PROMPT
        )
        
        completion = client.chat.completions.create(
            model=MODEL_1,
            messages=[
                {"role": "system", "content": ROLE_PROMPT},
                {"role": "user", "content": action_prompt},
            ],
            temperature=0.7,
            max_tokens=2000,
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"豆包AI分析错误: {str(e)}")
        return f"分析出错: {str(e)}"

# 使用OpenRouter分析
async def analyze_with_model_2(text):
    try:
        client = get_openrouter_client()
        action_prompt = ACTION_PROMPT_TEMPLATE.format(
            text=text,
            example_explanation_prompt=EXAMPLE_EXPLANATION_PROMPT
        )
        
        completion = client.chat.completions.create(
            model=MODEL_2,
            messages=[
                {"role": "system", "content": ROLE_PROMPT},
                {"role": "user", "content": action_prompt},
            ],
            temperature=0.7,
            max_tokens=2000,
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"GPT4o分析错误: {str(e)}")
        return f"分析出错: {str(e)}"

# 使用OpenRouter分析合同条款
async def analyze_contract_clauses(text):
    """
    分析合同条款并获取摘要和通俗解释
    
    Args:
        text (str): 合同文本内容
        
    Returns:
        str: 分析结果，包含条款摘要和通俗解释
    """
    try:
        client = get_openrouter_client()
        analysis_prompt = CLAUSE_ANALYSIS_PROMPT_TEMPLATE.format(text=text)
        
        completion = client.chat.completions.create(
            model=MODEL_1,  # 使用MODEL_1进行分析
            messages=[
                {"role": "user", "content": analysis_prompt}
            ],
            temperature=0.7,
            max_tokens=3000,  # 增加token限制以获取更完整的分析
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"合同条款分析错误: {str(e)}")
        return f"分析出错: {str(e)}"

# 使用豆包AI分析合同条款（备用方案）
async def analyze_contract_clauses_with_doubao(text):
    """
    使用豆包AI分析合同条款并获取摘要和通俗解释
    
    Args:
        text (str): 合同文本内容
        
    Returns:
        str: 分析结果，包含条款摘要和通俗解释
    """
    try:
        client = get_doubao_client()
        analysis_prompt = CLAUSE_ANALYSIS_PROMPT_TEMPLATE.format(text=text)
        
        completion = client.chat.completions.create(
            model=MODEL_2,  # 使用MODEL_2进行分析
            messages=[
                {"role": "user", "content": analysis_prompt}
            ],
            temperature=0.7,
            max_tokens=3000,  # 增加token限制以获取更完整的分析
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"豆包AI合同条款分析错误: {str(e)}")
        return f"分析出错: {str(e)}" 