<template>
  <div v-if="messages.length" class="space-y-4">
    <div v-for="(msg, idx) in messages" :key="idx">
      <!-- 用户消息 -->
      <UserMessage v-if="msg.role === 'user'" :content="msg.content" />
      
      <!-- AI助手消息 -->
      <AssistantMessage 
        v-else
        :content="msg.content"
        :isThinking="msg.isThinking"
        :isFollowUp="msg.isFollowUp"
        :followUpQuestions="msg.followUpQuestions"
        @selectQuestion="$emit('selectQuestion', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import UserMessage from './components/UserMessage.vue'
import AssistantMessage from './components/AssistantMessage.vue'

defineProps({
  messages: {
    type: Array,
    default: () => []
  }
})

defineEmits(['selectQuestion'])
</script> 