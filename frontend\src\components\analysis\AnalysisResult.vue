<template>
  <div class="analysis-result">
    <!-- 风险条款卡片列表 -->
    <div class="space-y-6">
      <RiskClauseCard
        v-for="(clause, index) in parsedClauses"
        :key="index"
        :clause="clause"
        :index="index"
      />
    </div>

    <!-- 总结部分 -->
    <SummarySection :summary="summary" />
  </div>
</template>

<script setup>
import RiskClauseCard from './components/RiskClauseCard.vue'
import SummarySection from './components/SummarySection.vue'
import { useClauseParser } from './composables/useClauseParser'

const props = defineProps({
  result: {
    type: String,
    required: true
  }
})

const { parsedClauses, summary } = useClauseParser(props.result)
</script>

<style>
.analysis-result {
  @apply space-y-8;
}
</style> 