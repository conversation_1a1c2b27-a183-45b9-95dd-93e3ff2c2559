# QA 消息组件

本目录包含了聊天消息列表的子组件，这些组件从 `QAMessageList.vue` 中拆分出来，以提高代码的模块化和可维护性。

## 组件结构

### UserMessage.vue
- **用途**: 显示用户发送的消息
- **Props**: 
  - `content`: 消息内容 (String, required)
- **样式**: 蓝色气泡，右对齐

### AssistantMessage.vue
- **用途**: 显示AI助手的回复消息
- **Props**:
  - `content`: 消息内容 (String)
  - `isThinking`: 是否正在思考 (Boolean)
  - `isFollowUp`: 是否为后续问题 (Boolean)
  - `followUpQuestions`: 后续问题列表 (Array)
- **事件**: 
  - `selectQuestion`: 选择后续问题时触发
- **依赖**: ThinkingIndicator, FollowUpQuestions

### ThinkingIndicator.vue
- **用途**: 显示AI正在思考的动画效果
- **Props**: 无
- **样式**: 跳动的圆点动画

### FollowUpQuestions.vue
- **用途**: 显示相关问题推荐列表
- **Props**:
  - `questions`: 问题列表 (Array)
- **事件**:
  - `selectQuestion`: 选择问题时触发
- **功能**: 支持展开/收起功能
- **依赖**: useExpandable composable

## 使用方式

```javascript
// 统一导入
import { UserMessage, AssistantMessage, ThinkingIndicator, FollowUpQuestions } from './components'

// 单独导入
import UserMessage from './components/UserMessage.vue'
import AssistantMessage from './components/AssistantMessage.vue'
```

## 重构优势

1. **代码分离**: 每个组件专注于单一功能
2. **可复用性**: 组件可以在其他地方独立使用
3. **易于测试**: 小组件更容易编写单元测试
4. **易于维护**: 降低代码复杂度，便于理解和修改
5. **更好的组织**: 清晰的目录结构和职责划分 