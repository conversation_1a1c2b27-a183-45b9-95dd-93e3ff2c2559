#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
from openai import OpenAI
from dotenv import load_dotenv
import PyPDF2

def load_config():
    """加载配置和环境变量"""
    # 加载.env文件中的环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("错误：未找到OPENAI_API_KEY环境变量")
        print("请在.env文件中设置您的OpenAI API密钥")
        sys.exit(1)
    
    return api_key

def read_pdf_file(pdf_path):
    """读取PDF文件内容"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = ""
            
            # 遍历所有页面并提取文本
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text_content += page.extract_text()
            
            return text_content.strip()
            
    except FileNotFoundError:
        print(f"错误：找不到PDF文件 '{pdf_path}'")
        sys.exit(1)
    except Exception as e:
        print(f"读取PDF文件时发生错误: {str(e)}")
        sys.exit(1)

def analyze_contract_with_ai(api_key, contract_content):
    """使用AI分析合同内容"""
    try:
        # 初始化OpenAI客户端
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=api_key
        )        
        
        print(f"正在使用AI分析合同条款...")
        print("-" * 50)
        
        # 构建合同分析prompt
        analysis_prompt = f"""请详细分析以下合同文档，并按照以下格式输出分析结果：

合同文档内容：
{contract_content}

请按照以下结构进行分析：

## 主要条款摘要

### 1. [条款名称]
**条款摘要：** [简要说明该条款的主要内容]
**通俗解释：** [用通俗易懂的语言解释该条款的含义和影响]

### 2. [条款名称]
**条款摘要：** [简要说明该条款的主要内容]
**通俗解释：** [用通俗易懂的语言解释该条款的含义和影响]

[继续列出所有识别出的条款...]

请确保：
1. 识别出合同中的所有重要条款
2. 每个条款都要有清晰的摘要和通俗解释
3. 使用通俗易懂的语言进行解释"""
        
        # 调用API
        response = client.chat.completions.create(
            model="openai/gpt-4.1",
            messages=[
                {"role": "user", "content": analysis_prompt}
            ]
        )
        
        # 获取回复内容
        reply = response.choices[0].message.content
        
        print("合同条款分析结果:")
        print("=" * 60)
        print(reply)
        print("=" * 60)
        
        return reply
        
    except Exception as e:
        print(f"合同分析过程中发生错误: {str(e)}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI合同分析工具 - 智能分析合同条款并提供通俗解释",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --contract contract.pdf
  python main.py --contract agreement.pdf
  python main.py --contract service_contract.pdf
        """
    )
    
    parser.add_argument(
        "--contract",
        required=True,
        help="要分析的合同PDF文件路径"
    )
    
    args = parser.parse_args()   
  
    print("=" * 60)
    print("        AI合同分析工具")
    print("=" * 60)
    
    # 加载配置
    api_key = load_config()
    
    # 读取合同PDF文件
    print(f"正在读取合同文件: {args.contract}")
    print("-" * 50)
    
    contract_content = read_pdf_file(args.contract)
    print(f"合同文件读取成功，内容长度: {len(contract_content)} 字符")
    print()
    
    # 使用AI分析合同内容
    analyze_contract_with_ai(api_key, contract_content)

if __name__ == "__main__":
    main()