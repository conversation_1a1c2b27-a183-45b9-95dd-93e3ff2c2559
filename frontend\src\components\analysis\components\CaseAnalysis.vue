<template>
  <div class="example-card border border-blue-200 rounded-lg overflow-hidden">
    <div 
      @click="toggle"
      class="example-card__header flex items-center justify-between px-4 py-2.5 bg-blue-600 cursor-pointer hover:bg-blue-700 transition-colors duration-200"
    >
      <div>
        <div class="flex items-center">
          <svg class="h-5 w-5 text-white mr-2" viewBox="0 0 24 24" fill="none">
            <path d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="text-base font-medium text-white">具体案例分析</span>
        </div>
      </div>
      <svg 
        class="h-5 w-5 text-white transform transition-transform duration-200"
        :class="{ 'rotate-180': isExpanded }"
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </div>
    <div 
      v-show="isExpanded"
      class="px-6 py-4 bg-white border-t border-blue-100 divide-y divide-gray-100"
    >
      <div class="pb-5">
        <div class="flex items-center mb-3">
          <svg class="h-5 w-5 text-gray-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <h5 class="text-base font-medium text-gray-900">案例故事</h5>
        </div>
        <div class="prose max-w-none">
          <p class="text-gray-700 leading-relaxed">{{ exampleExplanation }}</p>
        </div>
      </div>

      <div class="pt-5">
        <div class="flex items-center mb-3">
          <svg class="h-5 w-5 text-gray-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h5 class="text-base font-medium text-gray-900">分析思路</h5>
        </div>
        <div class="prose max-w-none">
          <p class="text-gray-700 leading-relaxed whitespace-pre-line">{{ reasoningProcess }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useExpandable } from '../composables/useExpandable'

defineProps({
  exampleExplanation: {
    type: String,
    required: true
  },
  reasoningProcess: {
    type: String,
    required: true
  }
})

const { isExpanded, toggle } = useExpandable()
</script>

<style scoped>
.example-card {
  transition: all 0.3s ease;
  user-select: none;
}

.example-card:hover .example-card__header {
  @apply bg-blue-700;
}

.example-card__header svg {
  transition: transform 0.3s ease;
}

.prose {
  font-size: 0.95rem;
}

.prose p {
  margin: 0;
  text-align: justify;
  line-height: 1.75;
}

/* 允许内容区域选择文本 */
.prose p {
  user-select: text;
}
</style> 