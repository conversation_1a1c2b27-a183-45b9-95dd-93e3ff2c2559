<template>
    <div class="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center"
        :class="{ 'border-blue-500 bg-blue-50': isDragging }" @dragover.prevent="isDragging = true"
        @dragleave.prevent="isDragging = false" @drop.prevent="handleFileDrop">
        <div class="flex justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>

        <p class="mt-4 text-lg font-medium text-gray-900">
            {{ selectedFile ? selectedFile.name : '拖放文件到此处或点击上传' }}
        </p>
        <p class="mt-2 text-sm text-gray-500">支持PDF格式，最大20MB</p>

        <input type="file" ref="fileInput" class="hidden" accept="application/pdf" @change="handleFileSelect" />
        <button v-if="!selectedFile" type="button"
            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="$refs.fileInput.click()">
            选择文件
        </button>

        <div v-if="selectedFile" class="mt-4 flex justify-center space-x-4">
            <button type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                @click="$emit('start-analysis')">
                开始分析
            </button>
            <button type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                @click="resetFile">
                取消
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['file-selected', 'start-analysis'])
const isDragging = ref(false)
const selectedFile = ref(null)
const fileInput = ref(null)

const validateFile = (file) => {
    if (!file) {
        return false
    }

    // 检查文件类型
    if (file.type !== 'application/pdf') {
        alert('请选择PDF文件')
        return false
    }

    // 检查文件大小
    if (file.size > 20 * 1024 * 1024) { // 20MB
        alert('文件大小不能超过20MB')
        return false
    }

    return true
}

const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (validateFile(file)) {
        selectedFile.value = file
        emit('file-selected', file)
    }
}

const handleFileDrop = (event) => {
    isDragging.value = false
    const file = event.dataTransfer.files[0]
    if (validateFile(file)) {
        selectedFile.value = file
        emit('file-selected', file)
    }
}

const resetFile = () => {
    selectedFile.value = null
    emit('file-selected', null)
}
</script>