import { ref, computed } from 'vue'

/**
 * 解析条款分析结果的组合函数
 * @param {string} clausesText - 条款分析的原始文本
 * @returns {Object} 包含解析后的条款数组
 */
export function useClausesParser(clausesText) {
  const parsedClauses = ref([])

  // 解析条款分析文本
  const parseClausesText = (text) => {
    if (!text || typeof text !== 'string') {
      return []
    }

    const clauses = []
    
    try {
      // 按照 "### " 分割条款
      const sections = text.split(/###\s+\d+\.\s+/)
      
      // 跳过第一个空的部分
      for (let i = 1; i < sections.length; i++) {
        const section = sections[i].trim()
        if (!section) continue

        // 提取条款标题（第一行）
        const lines = section.split('\n')
        const title = lines[0].trim()
        
        let content = ''
        let interpretation = ''
        
        // 查找条款摘要和通俗解释
        let currentSection = ''
        for (let j = 1; j < lines.length; j++) {
          const line = lines[j].trim()
          
          if (line.startsWith('**条款摘要：**')) {
            currentSection = 'content'
            content = line.replace('**条款摘要：**', '').trim()
          } else if (line.startsWith('**通俗解释：**')) {
            currentSection = 'interpretation'
            interpretation = line.replace('**通俗解释：**', '').trim()
          } else if (line && currentSection === 'content') {
            content += ' ' + line
          } else if (line && currentSection === 'interpretation') {
            interpretation += ' ' + line
          }
        }

        if (title && (content || interpretation)) {
          clauses.push({
            number: i,
            title: title,
            content: content || '暂无摘要',
            interpretation: interpretation || '暂无解释',
            badgeClass: getBadgeClass(i)
          })
        }
      }

      // 如果没有找到标准格式，尝试其他解析方式
      if (clauses.length === 0) {
        // 尝试按照数字编号分割
        const numberSections = text.split(/\d+\.\s+/)
        
        for (let i = 1; i < numberSections.length; i++) {
          const section = numberSections[i].trim()
          if (!section) continue

          const lines = section.split('\n').filter(line => line.trim())
          if (lines.length > 0) {
            const title = lines[0].trim()
            const content = lines.slice(1).join(' ').trim()
            
            clauses.push({
              number: i,
              title: title || `条款 ${i}`,
              content: content || section.substring(0, 200) + '...',
              interpretation: '请查看完整内容了解详情',
              badgeClass: getBadgeClass(i)
            })
          }
        }
      }

      // 如果仍然没有解析出内容，将整个文本作为一个条款
      if (clauses.length === 0 && text.trim()) {
        clauses.push({
          number: 1,
          title: '合同条款分析',
          content: text.substring(0, 500) + (text.length > 500 ? '...' : ''),
          interpretation: '完整的条款分析内容，请查看详细信息',
          badgeClass: 'bg-blue-100 text-blue-800'
        })
      }

    } catch (error) {
      console.error('解析条款分析文本时出错:', error)
      // 出错时返回原始文本
      if (text.trim()) {
        clauses.push({
          number: 1,
          title: '条款分析结果',
          content: text,
          interpretation: '原始分析结果',
          badgeClass: 'bg-gray-100 text-gray-800'
        })
      }
    }

    return clauses
  }

  // 获取徽章样式
  const getBadgeClass = (index) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800'
    ]
    return colors[(index - 1) % colors.length]
  }

  // 计算属性：解析后的条款
  const clauses = computed(() => {
    return parseClausesText(clausesText)
  })

  return {
    clauses,
    parsedClauses
  }
}
