<template>
  <div class="fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col border-l border-gray-100">
    <QAAssistantHeader @close="$emit('close')" />
    <div ref="messagesContainer" class="flex-1 overflow-y-auto px-6 py-6 bg-gray-50">
      <div class="mb-6 bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
        <!-- 欢迎消息 -->
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
          </div>
          <div class="flex-1">
            <div class="text-gray-800 text-sm leading-relaxed">
              您好！我是您的合同分析助手。您可以询问关于这份合同的任何问题。
            </div>
          </div>
        </div>
      </div>
      <QASuggestedQuestions :questions="questions" @select="selectQuestion" />
      <QAMessageList :messages="messages" @selectQuestion="selectQuestion" />
    </div>
    <div class="p-4 bg-white border-t border-gray-100">
      <QAInputBox v-model="input" @send="send" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import QAAssistantHeader from './qa/QAAssistantHeader.vue'
import QASuggestedQuestions from './qa/QASuggestedQuestions.vue'
import QAMessageList from './qa/QAMessageList.vue'
import QAInputBox from './qa/QAInputBox.vue'
import { useContractQA } from './qa/composables/useContractQA'

const props = defineProps({
  questions: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['close'])

const { input, messages, selectQuestion, send } = useContractQA()

const messagesContainer = ref(null)

// 监听消息列表变化，滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 滚动到底部的函数
function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}
</script>

<style scoped>
/* 可根据需要自定义样式 */
</style> 
