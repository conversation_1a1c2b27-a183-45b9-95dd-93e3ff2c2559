<template>
  <div class="mb-6">
    <div class="flex items-center mb-3">
      <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
      </svg>
      <span class="font-medium text-gray-700">推荐问题</span>
    </div>
    <div class="space-y-2">
      <button
        v-for="(q, idx) in questions" :key="idx"
        class="w-full text-left px-4 py-3 bg-white hover:bg-blue-50 hover:border-blue-200 rounded-xl border border-gray-200 text-sm text-gray-700 transition-all duration-200 shadow-sm hover:shadow-md"
        @click="$emit('select', q)"
      >
        <div class="flex items-center justify-between">
          <span>{{ q }}</span>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  questions: {
    type: Array,
    default: () => []
  }
})
defineEmits(['select'])
</script> 