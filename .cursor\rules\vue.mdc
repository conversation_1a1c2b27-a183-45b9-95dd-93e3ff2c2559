---
description: 
globs: 
alwaysApply: true
---
---
description: Vue.js best practices and patterns for modern web applications
globs: **/*.vue, **/*.ts, components/**/*
---

# Vue.js Best Practices

## Component Structure
- Use Composition API over Options API
- Keep components small and focused
- Implement proper props validation
- Use proper emit declarations
- Keep template logic minimal

## Composition API
- Use proper ref and reactive
- Implement proper lifecycle hooks
- Use composables for reusable logic
- Keep setup function clean
- Use proper computed properties
- Implement proper watchers

## State Management
- Use Pinia for state management
- Keep stores modular
- Use proper state composition
- Implement proper actions
- Use proper getters
- Handle async state properly

## Performance
- Use proper component lazy loading
- Implement proper caching
- Use proper computed properties
- Avoid unnecessary watchers
- Use proper v-show vs v-if
- Implement proper key management

## Routing
- Use Vue Router properly
- Implement proper navigation guards
- Use proper route meta fields
- Handle route params properly
- Implement proper lazy loading
- Use proper navigation methods

## Forms
- Use v-model properly
- Implement proper validation
- Handle form submission properly
- Show proper loading states
- Use proper error handling
- Implement proper form reset

## Testing
- Write proper unit tests
- Implement proper component tests
- Use Vue Test Utils properly
- Test composables properly
- Implement proper mocking
- Test async operations

## Best Practices
- Follow Vue style guide
- Use proper naming conventions
- Keep components organized
- Implement proper error handling
- Use proper event handling
- Document complex logic

## Build and Tooling
- Use Vite for development
- Use Javascript instead of Typescript
- Configure proper build setup
- Use proper environment variables
- Implement proper code splitting
- Use proper asset handling
- Configure proper optimization 
