<template>
  <div class="bg-white rounded-lg shadow-lg overflow-hidden">
    <!-- 条款标题栏 -->
    <div 
      @click="toggle"
      class="flex items-center justify-between px-6 py-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors duration-200"
    >
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <span class="text-lg font-semibold text-gray-900">条款</span>
          <span class="ml-2 text-lg font-semibold text-gray-900">{{ index + 1 }}</span>
          <div class="ml-4 flex items-center">
            <svg class="h-5 w-5 mr-1" :class="getRiskLevelIconColor(clause.riskLevel)" viewBox="0 0 24 24" fill="none">
              <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span :class="getRiskLevelTextColor(clause.riskLevel)" class="text-sm font-medium">{{ clause.riskLevel }}级风险</span>
          </div>
        </div>
      </div>
      <svg 
        class="h-6 w-6 text-gray-500 transform transition-transform duration-200"
        :class="{ 'rotate-180': isExpanded }"
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </div>
    
    <!-- 条款详细内容 -->
    <div 
      v-show="isExpanded"
      class="px-6 py-4 space-y-4 transition-all duration-200"
    >
      <!-- 条款内容 -->
      <InfoBlock
        title="条款内容"
        :content="clause.content"
        container-class="bg-[#F8F9FC] px-4 py-2.5 rounded-lg"
        title-class="text-[#334415]"
        content-class="text-[#334415]"
      >
        <template #icon>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-[1em] w-[1em] text-[#334415] mr-2 translate-y-[0.15em]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
            <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
            <path d="M10 9H8"></path>
            <path d="M16 13H8"></path>
            <path d="M16 17H8"></path>
          </svg>
        </template>
      </InfoBlock>
      
      <!-- 风险说明 -->
      <InfoBlock
        title="风险说明"
        :content="clause.riskDescription"
        container-class="bg-red-50 px-4 py-2.5 rounded-lg"
        title-class="text-red-800"
        content-class="text-red-900"
      >
        <template #icon>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-[1em] w-[1em] text-red-600 mr-2 translate-y-[0.15em]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" x2="12" y1="8" y2="12"></line>
            <line x1="12" x2="12.01" y1="16" y2="16"></line>
          </svg>
        </template>
      </InfoBlock>
      
      <!-- 潜在后果 -->
      <InfoBlock
        title="潜在后果"
        :content="clause.consequences"
        container-class="bg-yellow-50 px-4 py-2.5 rounded-lg"
        title-class="text-yellow-800"
        content-class="text-yellow-900"
      >
        <template #icon>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-[1em] w-[1em] text-yellow-600 mr-2 translate-y-[0.15em]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </template>
      </InfoBlock>
      
      <!-- 修改建议 -->
      <InfoBlock
        title="修改建议"
        :content="clause.suggestions"
        container-class="bg-green-50 px-4 py-2.5 rounded-lg"
        title-class="text-green-800"
        content-class="text-green-900"
      >
        <template #icon>
          <svg class="h-[1em] w-[1em] text-green-600 mr-2 translate-y-[0.15em]" viewBox="0 0 24 24" fill="none">
            <path d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </template>
      </InfoBlock>
      
      <!-- 实例解释 -->
      <CaseAnalysis
        :example-explanation="clause.exampleExplanation"
        :reasoning-process="clause.reasoningProcess"
      />
    </div>
  </div>
</template>

<script setup>
import { useExpandable } from '../composables/useExpandable'
import { getRiskLevelIconColor, getRiskLevelTextColor } from '../constants/riskLevelStyles'
import InfoBlock from './InfoBlock.vue'
import CaseAnalysis from './CaseAnalysis.vue'

defineOptions({
  name: 'RiskClauseCard'
})

const props = defineProps({
  clause: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const { isExpanded, toggle } = useExpandable()
</script>

<style scoped>
/* Scoped styles can be removed as they are now in CaseAnalysis.vue */
</style> 