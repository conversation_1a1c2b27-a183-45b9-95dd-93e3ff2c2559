<template>
  <div>
    <AnalysisTabs :active-tab="activeTab" @update:activeTab="$emit('update:activeTab', $event)" />
    <div class="mt-6">
      <div v-if="activeTab === 'model_1'" class="prose max-w-none">
        <AnalysisResult :result="analysisResult.model_1_result" />
      </div>
      <div v-if="activeTab === 'model_2'" class="prose max-w-none">
        <AnalysisResult :result="analysisResult.model_2_result" />
      </div>
      <CompareView
        v-if="activeTab === 'compare'"
        :model1-result="analysisResult.model_1_result"
        :model2-result="analysisResult.model_2_result"
      />
    </div>
  </div>
</template>

<script setup>
import AnalysisResult from './AnalysisResult.vue'
import AnalysisTabs from './AnalysisTabs.vue'
import CompareView from './CompareView.vue'

defineProps({
  analysisResult: {
    type: Object,
    required: true
  },
  activeTab: {
    type: String,
    required: true
  }
})

defineEmits(['update:activeTab'])
</script> 