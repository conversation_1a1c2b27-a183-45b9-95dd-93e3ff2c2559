import { ref } from 'vue'
import { PROGRESS_MESSAGES, PROGRESS_CONFIG } from '../constants'

export function useProgress() {
  // 进度状态
  const progress = ref(0)
  const message = ref("正在准备分析...")
  const isProcessing = ref(false)

  // 更新进度
  const updateProgress = (value) => {
    progress.value = Math.min(Math.max(value, 0), 100)
  }

  // 更新消息
  const updateMessage = (key) => {
    if (PROGRESS_MESSAGES[key]) {
      message.value = PROGRESS_MESSAGES[key]
    }
  }

  // 开始处理
  const startProcessing = () => {
    isProcessing.value = true
    progress.value = PROGRESS_CONFIG.INITIAL
    updateMessage('uploading')
  }

  // 完成处理
  const completeProcessing = () => {
    isProcessing.value = false
    progress.value = PROGRESS_CONFIG.COMPLETE
  }

  // 重置进度
  const resetProgress = () => {
    progress.value = 0
    message.value = "正在准备分析..."
    isProcessing.value = false
  }

  // 根据进度自动更新消息
  const updateMessageByProgress = () => {
    if (progress.value < 30) {
      updateMessage('extracting')
    } else if (progress.value < 60) {
      updateMessage('model1Analyzing')
    } else {
      updateMessage('model2Analyzing')
    }
  }

  return {
    // 状态
    progress,
    message,
    isProcessing,

    // 方法
    updateProgress,
    updateMessage,
    startProcessing,
    completeProcessing,
    resetProgress,
    updateMessageByProgress
  }
} 