{"name": "contract-analysis-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "axios": "^1.6.0", "marked": "^4.3.0", "vue": "^3.3.4", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@vitejs/plugin-vue": "^4.4.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "vite": "^4.4.11"}}