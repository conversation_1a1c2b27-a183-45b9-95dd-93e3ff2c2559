from fastapi import Fast<PERSON><PERSON>, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uuid
import os
import time
import asyncio
import logging
import json
from ai_client import analyze_with_model_1, analyze_with_model_2, analyze_contract_clauses
from utils.pdf import read_pdf

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="合同风险分析系统")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 存储分析任务的结果
analysis_results = {}

# 结果存储目录
RESULTS_DIR = "results"
os.makedirs(RESULTS_DIR, exist_ok=True)


# 保存分析结果到文件
def save_analysis_result(task_id, result):
    file_path = os.path.join(RESULTS_DIR, f"{task_id}.json")
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

# 从文件加载分析结果
def load_analysis_result(task_id):
    file_path = os.path.join(RESULTS_DIR, f"{task_id}.json")
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

# 后台任务：分析合同
async def analyze_contract_task(task_id, file_path):
    try:
        # 更新任务状态
        analysis_results[task_id] = {
            "status": "processing",
            "model_1_result": None,
            "model_2_result": None,
            "error": None,
            "start_time": time.time()
        }
        
        # 读取PDF
        text = read_pdf(file_path)
        
        # 并行执行三个AI分析：风险分析（模型1和模型2）+ 条款分析
        model_1_task = asyncio.create_task(analyze_with_model_1(text))
        model_2_task = asyncio.create_task(analyze_with_model_2(text))
        clauses_task = asyncio.create_task(analyze_contract_clauses(text))

        # 等待三个任务完成
        model_1_result, model_2_result, clauses_result = await asyncio.gather(
            model_1_task, model_2_task, clauses_task
        )
        
        # 更新结果
        end_time = time.time()
        result = {
            "id": task_id,
            "status": "completed",
            "model_1_result": model_1_result,
            "model_2_result": model_2_result,
            "clauses_analysis": clauses_result,
            "error": None,
            "start_time": analysis_results[task_id]["start_time"],
            "end_time": end_time,
            "duration": end_time - analysis_results[task_id]["start_time"]
        }
        
        # 保存结果到内存和文件
        analysis_results[task_id] = result
        save_analysis_result(task_id, result)
        
        # 清理临时文件
        os.remove(file_path)
        
    except Exception as e:
        logger.error(f"分析任务错误: {str(e)}")
        result = {
            "id": task_id,
            "status": "failed",
            "model_1_result": None,
            "model_2_result": None,
            "error": str(e),
            "start_time": analysis_results[task_id]["start_time"],
            "end_time": time.time()
        }
        analysis_results[task_id] = result
        save_analysis_result(task_id, result)
        
        # 尝试清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except:
            pass

# API端点：上传合同并开始分析
@app.post("/api/upload")
async def upload_file(background_tasks: BackgroundTasks, file: UploadFile = File(...)):
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建临时目录（如果不存在）
        os.makedirs("temp", exist_ok=True)
        
        # 保存上传的文件
        file_path = f"temp/{task_id}_{file.filename}"
        with open(file_path, "wb") as f:
            f.write(await file.read())
        
        # 启动后台任务
        background_tasks.add_task(analyze_contract_task, task_id, file_path)
        
        return {"task_id": task_id, "status": "processing"}
    
    except Exception as e:
        logger.error(f"上传处理错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# API端点：获取分析结果
@app.get("/api/analysis/{task_id}")
async def get_analysis_result(task_id: str):
    # 首先检查内存中的结果
    if task_id in analysis_results:
        result = analysis_results[task_id]
        # 如果任务已完成且已经过去超过1小时，清理内存中的结果
        if result["status"] in ["completed", "failed"] and "end_time" in result:
            if time.time() - result["end_time"] > 3600:  # 1小时
                # 返回结果前先复制一份
                response_data = result.copy()
                # 删除内存中的结果
                del analysis_results[task_id]
                return response_data
        return result
    
    # 如果内存中没有，尝试从文件加载
    result = load_analysis_result(task_id)
    if result:
        return result
    
    raise HTTPException(status_code=404, detail="任务不存在")

# API端点：获取分析状态
@app.get("/api/analysis/status/{task_id}")
async def get_analysis_status(task_id: str):
    # 首先检查内存中的结果
    if task_id in analysis_results:
        result = analysis_results[task_id]
        if result["status"] == "processing":
            # 计算进度（这里可以根据实际情况调整进度计算逻辑）
            elapsed_time = time.time() - result["start_time"]
            progress = min(int(elapsed_time * 10), 90)  # 假设分析需要10秒，每秒增加10%的进度
            return {
                "status": "processing",
                "progress": progress
            }
        return {
            "status": result["status"],
            "progress": 100 if result["status"] == "completed" else 0,
            "result": result if result["status"] == "completed" else None
        }
    
    # 如果内存中没有，尝试从文件加载
    result = load_analysis_result(task_id)
    if result:
        return {
            "status": result["status"],
            "progress": 100 if result["status"] == "completed" else 0,
            "result": result if result["status"] == "completed" else None
        }
    
    raise HTTPException(status_code=404, detail="任务不存在")

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
