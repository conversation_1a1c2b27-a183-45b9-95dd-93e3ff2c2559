/**
 * 格式化持续时间
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export function formatDuration(seconds) {
  if (!seconds) return "0秒"
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  const parts = []
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`)
  
  return parts.join("")
}

/**
 * 计算风险条款数量
 * @param {Object} analysis - 分析结果对象
 * @returns {number} 风险条款数量
 */
export function countRiskClauses(analysis) {
  if (!analysis || !analysis.risk_clauses) return 0
  return analysis.risk_clauses.length
}

/**
 * 获取分析详细程度
 * @param {Object} analysis - 分析结果对象
 * @returns {string} 详细程度描述
 */
export function getDetailLevel(analysis) {
  if (!analysis) return "未知"
  
  const totalClauses = countRiskClauses(analysis)
  if (totalClauses === 0) return "基础"
  if (totalClauses < 5) return "中等"
  return "详细"
}

/**
 * 获取建议质量评级
 * @param {Object} analysis - 分析结果对象
 * @returns {string} 质量评级
 */
export function getSuggestionQuality(analysis) {
  if (!analysis || !analysis.suggestions) return "未知"
  
  const suggestions = analysis.suggestions
  const hasSpecific = suggestions.some(s => s.includes("具体"))
  const hasAlternative = suggestions.some(s => s.includes("替代方案"))
  const hasReference = suggestions.some(s => s.includes("参考"))
  
  if (hasSpecific && hasAlternative && hasReference) return "优秀"
  if ((hasSpecific && hasAlternative) || (hasSpecific && hasReference)) return "良好"
  if (hasSpecific || hasAlternative || hasReference) return "一般"
  return "基础"
} 