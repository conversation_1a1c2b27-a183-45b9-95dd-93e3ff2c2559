export const RISK_LEVEL_STYLES = {
  '高': 'bg-red-100 text-red-800',
  '中': 'bg-yellow-100 text-yellow-800',
  '低': 'bg-green-100 text-green-800'
}

export const RISK_LEVEL_COLORS = {
  '高': {
    icon: 'text-red-500',
    text: 'text-red-500'
  },
  '中': {
    icon: 'text-yellow-500',
    text: 'text-yellow-500'
  },
  '低': {
    icon: 'text-green-500',
    text: 'text-green-500'
  }
}

export const getRiskLevelClass = (level) => {
  return `px-3 py-1 rounded-full text-sm font-medium ${RISK_LEVEL_STYLES[level] || RISK_LEVEL_STYLES['中']}`
}

export const getRiskLevelIconColor = (level) => {
  return RISK_LEVEL_COLORS[level]?.icon || RISK_LEVEL_COLORS['中'].icon
}

export const getRiskLevelTextColor = (level) => {
  return RISK_LEVEL_COLORS[level]?.text || RISK_LEVEL_COLORS['中'].text
} 