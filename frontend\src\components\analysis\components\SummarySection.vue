<template>
  <div class="mt-8 bg-white rounded-lg shadow-lg overflow-hidden">
    <div class="px-6 py-4 bg-gray-50">
      <h3 class="text-xl font-semibold text-gray-900">总体评估</h3>
    </div>
    <div class="p-6 space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 总体风险等级 -->
        <div class="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-red-800 mb-2">总体风险等级</h4>
          <p class="text-2xl font-bold text-red-900">{{ summary.riskLevel }}</p>
        </div>
        
        <!-- 主要风险点 -->
        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg md:col-span-2">
          <h4 class="text-sm font-medium text-yellow-800 mb-2">主要风险点</h4>
          <p class="text-yellow-900">{{ summary.mainRisks }}</p>
        </div>
      </div>
      
      <!-- 建议优先处理 -->
      <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-green-800 mb-2">建议优先处理</h4>
        <p class="text-green-900">{{ summary.prioritySuggestions }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'SummarySection'
})

defineProps({
  summary: {
    type: Object,
    required: true
  }
})
</script> 