<template>
  <div class="flex items-start space-x-3">
    <div class="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
      <svg v-if="!isThinking" class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
      </svg>
      <!-- 思考动画图标 -->
      <svg v-else class="w-4 h-4 text-white animate-spin" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
      </svg>
    </div>
    <div class="flex-1">
      <!-- 正常回答消息 -->
      <div v-if="!isFollowUp" class="bg-white px-4 py-2 rounded-2xl rounded-bl-md max-w-xs shadow-sm border border-gray-100">
        <!-- 思考中的动画内容 -->
        <ThinkingIndicator v-if="isThinking" />
        <!-- 正常消息内容 -->
        <div v-else>
          {{ content }}
        </div>
      </div>
      
      <!-- 后续问题消息 -->
      <FollowUpQuestions 
        v-else 
        :questions="followUpQuestions"
        @selectQuestion="$emit('selectQuestion', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import ThinkingIndicator from './ThinkingIndicator.vue'
import FollowUpQuestions from './FollowUpQuestions.vue'

defineProps({
  content: {
    type: String,
    default: ''
  },
  isThinking: {
    type: Boolean,
    default: false
  },
  isFollowUp: {
    type: Boolean,
    default: false
  },
  followUpQuestions: {
    type: Array,
    default: () => []
  }
})

defineEmits(['selectQuestion'])
</script> 