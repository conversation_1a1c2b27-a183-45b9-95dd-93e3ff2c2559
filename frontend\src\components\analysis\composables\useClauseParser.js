import { computed } from 'vue'

export function useClauseParser(text) {
  const parsedClauses = computed(() => {
    const clauses = []
    
    try {
      // 分离风险条款和总结部分
      const [clausesText, summaryText] = text.split('【总结】')
      
      // 改进的正则表达式，更精确地匹配每个部分
      const clausePattern = /(\d+)\.\s*条款内容：([\s\S]*?)\s*风险等级：([\s\S]*?)\s*风险说明：([\s\S]*?)\s*潜在后果：([\s\S]*?)\s*修改建议：([\s\S]*?)\s*实例解释：([\s\S]*?)\s*推理过程：([\s\S]*?)(?=\s*\d+\.\s*条款内容：|$)/g
      
      const clauseMatches = clausesText.matchAll(clausePattern)
      
      for (const match of clauseMatches) {
        clauses.push({
          number: match[1],
          content: match[2].trim(),
          riskLevel: match[3].trim(),
          riskDescription: match[4].trim(),
          consequences: match[5].trim(),
          suggestions: match[6].trim(),
          exampleExplanation: match[7].trim(),
          reasoningProcess: match[8].trim()
        })
      }

      // 添加错误处理和验证
      if (clauses.length === 0) {
        console.warn('未找到任何风险条款，可能是格式不正确')
      }

      // 验证每个条款的完整性
      clauses.forEach((clause, index) => {
        if (!clause.content || !clause.riskLevel || !clause.riskDescription || 
            !clause.consequences || !clause.suggestions || !clause.exampleExplanation || !clause.reasoningProcess) {
          console.warn(`第${index + 1}个条款的某些字段为空，可能解析不完整`)
        }
      })
    } catch (error) {
      console.error('解析分析结果时出错:', error)
    }
    
    return clauses
  })

  const summary = computed(() => {
    try {
      const summaryText = text.split('【总结】')[1]
      
      // 解析总结部分
      const riskLevelMatch = summaryText.match(/1\.\s*总体风险等级：([\s\S]*?)(?=2\.|$)/)
      const mainRisksMatch = summaryText.match(/2\.\s*主要风险点：([\s\S]*?)(?=3\.|$)/)
      const suggestionsMatch = summaryText.match(/3\.\s*建议优先处理：([\s\S]*?)$/)
      
      return {
        riskLevel: riskLevelMatch ? riskLevelMatch[1].trim() : '未知',
        mainRisks: mainRisksMatch ? mainRisksMatch[1].trim() : '无',
        prioritySuggestions: suggestionsMatch ? suggestionsMatch[1].trim() : '无'
      }
    } catch (error) {
      console.error('解析总结时出错:', error)
      return {
        riskLevel: '解析错误',
        mainRisks: '解析错误',
        prioritySuggestions: '解析错误'
      }
    }
  })

  return {
    parsedClauses,
    summary
  }
} 