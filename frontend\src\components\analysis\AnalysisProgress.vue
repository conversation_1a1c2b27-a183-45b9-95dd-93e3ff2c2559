<template>
  <div v-if="isAnalyzing" class="p-6">
    <div class="text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-blue-500 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
      <h2 class="mt-4 text-lg font-medium text-gray-900">{{ progressMessage }}</h2>
      <div class="mt-4 w-full bg-gray-200 rounded-full h-2.5">
        <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${analysisProgress}%` }"></div>
      </div>
      <p class="mt-2 text-sm text-gray-500">分析可能需要1-2分钟，请耐心等待...</p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isAnalyzing: {
    type: Boolean,
    required: true
  },
  progressMessage: {
    type: String,
    required: true
  },
  analysisProgress: {
    type: Number,
    required: true
  }
})
</script> 