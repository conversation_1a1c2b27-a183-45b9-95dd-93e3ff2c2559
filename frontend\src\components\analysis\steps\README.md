# 分析步骤组件

这个目录包含了分析流程的各个步骤组件，这些组件是从 `App.vue` 重构过程中拆分出来的。

## 重构背景

原来的 `App.vue` 组件承担了太多职责：
- 路由管理
- 状态管理  
- UI渲染
- 业务逻辑

重构后，职责被合理分配到不同的组件中。

## 组件结构

### FileUploadStep.vue
- **用途**: 文件上传步骤的UI包装器
- **职责**: 封装文件上传界面，传递事件
- **Props**: 无
- **Events**: 
  - `file-selected`: 文件选择时触发
  - `start-analysis`: 开始分析时触发

### AnalysisStep.vue  
- **用途**: 分析进度步骤的UI包装器
- **职责**: 封装分析进度界面
- **Props**:
  - `isAnalyzing`: 是否正在分析 (Boolean)
  - `progressMessage`: 进度消息 (String)
  - `analysisProgress`: 分析进度 (Number)

### ResultStep.vue
- **用途**: 分析结果步骤的UI包装器
- **职责**: 封装分析结果界面，传递事件
- **Props**:
  - `analysisResult`: 分析结果对象 (Object)
- **Events**:
  - `reset-analysis`: 重置分析时触发
  - `further-analysis`: 进一步分析时触发

## 父组件

### AnalysisWorkflow.vue
- **用途**: 管理分析流程的状态和步骤切换
- **职责**: 
  - 计算当前应该显示的步骤
  - 管理分析相关的状态
  - 处理路由逻辑
  - 协调各个步骤之间的交互

## 重构优势

1. **单一职责**: 每个组件都有明确的单一职责
2. **可维护性**: 代码结构更清晰，便于理解和修改
3. **可测试性**: 小组件更容易编写单元测试
4. **可复用性**: 步骤组件可以在其他流程中复用
5. **关注点分离**: UI、状态管理、路由逻辑分离

## 使用方式

```vue
<template>
  <AnalysisWorkflow />
</template>

<script setup>
import AnalysisWorkflow from './components/analysis/AnalysisWorkflow.vue'
</script>
```

工作流组件会自动管理步骤的切换和状态。 