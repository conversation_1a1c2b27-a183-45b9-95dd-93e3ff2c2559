---
description: 
globs: 
alwaysApply: true
---
---
description: Applies general Python coding best practices across all Python files in the project, focusing on code clarity, style, and maintainability.
globs: **/*.py
---
- Follow PEP 8 style guide for formatting.
- Use docstrings to document functions and classes.
- Write simple and clear code; avoid unnecessary complexity.
- Prefer list comprehensions for creating lists when appropriate.
- Use try-except blocks to handle exceptions gracefully.
- Utilize type hints for better code clarity and type checking.
- Limit the use of global variables to reduce side effects.
- Choose descriptive variable, function, and class names.