<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />
    <MainContent>
      <AnalysisWorkflow />
    </MainContent>
    <AppFooter />
  </div>
</template>

<script setup>
import AppFooter from './components/layout/AppFooter.vue'
import AppHeader from './components/layout/AppHeader.vue'
import MainContent from './components/layout/MainContent.vue'
import AnalysisWorkflow from './components/analysis/AnalysisWorkflow.vue'
</script>

<style>
/* 可以添加额外的样式，但大部分样式已通过Tailwind类实现 */
</style>