// API配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8001"

// 轮询配置
export const POLLING_INTERVAL = 2000 // 轮询间隔时间（毫秒）

// 进度消息映射
export const PROGRESS_MESSAGES = {
  uploading: "正在上传文件...",
  uploaded: "文件上传成功，正在进行AI分析...",
  extracting: "正在提取合同文本...",
  model1Analyzing: "模型1正在分析合同风险条款...",
  model2Analyzing: "模型2正在分析合同风险条款..."
}

// 进度配置
export const PROGRESS_CONFIG = {
  INITIAL: 5,    // 初始进度
  UPLOADED: 15,  // 上传完成后的进度
  MAX: 95,       // 最大进度（完成前）
  COMPLETE: 100  // 完成进度
} 