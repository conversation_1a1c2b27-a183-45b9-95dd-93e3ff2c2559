<template>
  <div v-if="analysisResult" class="divide-y divide-gray-200">
    <div class="p-6">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-gray-900">分析结果</h2>
        <div class="flex space-x-3">
          <button 
            type="button" 
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="$emit('reset-analysis')"
          >
            重新分析
          </button>
          <button 
            type="button" 
            class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded-md shadow-sm text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="openQA"
          >
            文档对话
          </button>
        </div>
      </div>
      <AnalysisStats
        :duration="analysisResult.duration"
        :model-result="analysisResult.model_1_result"
      />
    </div>

    <!-- 新增的风险分析和关键条款切换按钮 -->
    <div class="px-6 py-4 bg-gray-50">
      <div class="flex space-x-3">
        <button
          @click="contentType = 'risk'"
          :class="[
            'px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
            contentType === 'risk'
              ? 'bg-blue-600 text-white shadow-md transform scale-105'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
          ]"
        >
          <span class="mr-2">⚠️</span>
          风险分析
        </button>
        <button
          @click="contentType = 'clauses'"
          :class="[
            'px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
            contentType === 'clauses'
              ? 'bg-blue-600 text-white shadow-md transform scale-105'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
          ]"
        >
          <span class="mr-2">📋</span>
          条款摘要
        </button>
      </div>
    </div>

    <!-- 内容显示区域 -->
    <div class="p-6">
      <!-- 风险分析内容 -->
      <RiskAnalysisPanel
        v-if="contentType === 'risk'"
        v-model:activeTab="activeTab"
        :analysis-result="analysisResult"
      />

      <!-- 关键条款解读内容 -->
      <KeyClausesPanel
        v-if="contentType === 'clauses'"
        :clauses-analysis="analysisResult?.clauses_analysis"
        :is-loading="!analysisResult?.clauses_analysis"
      />
    </div>

    <ContractQAAssistant v-if="showQA" :questions="recommendedQuestions" @close="closeQA" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import AnalysisStats from './AnalysisStats.vue'
import ContractQAAssistant from './ContractQAAssistant.vue'
import RiskAnalysisPanel from './RiskAnalysisPanel.vue'
import KeyClausesPanel from './KeyClausesPanel.vue'

const props = defineProps({
  analysisResult: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['reset-analysis', 'further-analysis'])

const activeTab = ref('model_1')
const showQA = ref(false)
const contentType = ref('risk') // 'risk' 或 'clauses'

const recommendedQuestions = [
  '房屋产权纠纷时甲方的具体赔偿范围是否明确？',
  '押金退还的房屋损耗认定标准是否有细化约定？',
  '乙方续租时租金涨幅是否受合同条款限制？'
]

function openQA() {
  showQA.value = true
}
function closeQA() {
  showQA.value = false
}

// 如果分析结果变化，重置tab和内容类型
watch(() => props.analysisResult, () => {
  activeTab.value = 'model_1'
  contentType.value = 'risk'
})
</script> 