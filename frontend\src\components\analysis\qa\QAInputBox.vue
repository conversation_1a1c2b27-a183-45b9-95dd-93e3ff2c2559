<template>
  <form @submit.prevent="onSend">
    <div class="relative">
      <textarea
        v-model="inputValue"
        rows="3"
        class="w-full border border-gray-200 rounded-2xl px-4 py-3 pr-14 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-gray-400 text-sm"
        placeholder="请输入您的问题"
        @keydown.enter.exact.prevent="onSend"
      />
      <button 
        type="submit" 
        class="absolute bottom-3 right-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-2.5 rounded-full flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50"
        :disabled="!inputValue.trim()"
      >
        <svg class="w-4 h-4 transform rotate-45" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
        </svg>
      </button>
    </div>
    <div class="mt-2 text-xs text-gray-400 px-1">
      按 Enter 发送，Ctrl + Enter 换行
    </div>
  </form>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:modelValue', 'send'])

const inputValue = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
})

function onSend() {
  if (!inputValue.value.trim()) return
  emit('send')
}
</script> 