import { watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export function useAnalysisRouter(analysisResult, loadAnalysisResult) {
  const route = useRoute()
  const router = useRouter()

  // 监听分析结果变化，更新路由
  watch(analysisResult, (newResult) => {
    if (newResult?.id) {
      router.push(`/analysis/${newResult.id}`)
    }
  })

  // 监听路由参数变化，加载分析结果
  watch(() => route.params.id, async (newId) => {
    if (newId && (!analysisResult.value || analysisResult.value.id !== newId)) {
      await loadAnalysisResult(newId)
    }
  }, { immediate: true })

  return {
    route,
    router
  }
} 