<template>
  <div :class="containerClass">
    <div class="inline-flex items-baseline mb-1.5">
      <slot name="icon"></slot>
      <h4 :class="['text-base', 'font-medium', titleClass]">{{ title }}</h4>
    </div>
    <p :class="['mt-1', contentClass]">{{ content }}</p>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  containerClass: {
    type: String,
    default: ''
  },
  titleClass: {
    type: String,
    default: ''
  },
  contentClass: {
    type: String,
    default: ''
  }
})
</script> 