<template>
  <div class="px-6 py-4">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button
          @click="$emit('update:activeTab', 'model_1')"
          class="py-4 px-1 border-b-2 font-medium text-sm"
          :class="activeTab === 'model_1' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        >
          模型1 分析
        </button>
        <button
          @click="$emit('update:activeTab', 'model_2')"
          class="py-4 px-1 border-b-2 font-medium text-sm"
          :class="activeTab === 'model_2' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        >
          模型2 分析
        </button>
        <button
          @click="$emit('update:activeTab', 'compare')"
          class="py-4 px-1 border-b-2 font-medium text-sm"
          :class="activeTab === 'compare' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        >
          对比视图
        </button>
      </nav>
    </div>
  </div>
</template>

<script setup>
defineProps({
  activeTab: {
    type: String,
    required: true
  }
})

defineEmits(['update:activeTab'])
</script> 