<template>
  <div class="mt-3">
    <div class="bg-blue-50 border border-blue-200 rounded-2xl p-4 shadow-sm">
      <div class="flex items-center mb-3 cursor-pointer" @click="toggle">
        <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
        </svg>
        <span class="font-medium text-blue-700 text-sm">相关问题推荐</span>
        <svg 
          class="w-4 h-4 text-blue-500 ml-2 transform transition-transform duration-200"
          :class="{ 'rotate-180': isExpanded }"
          fill="none" 
          stroke="currentColor" 
          stroke-width="2" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"/>
        </svg>
      </div>
      <div 
        v-show="isExpanded"
        class="space-y-2 transition-all duration-200"
      >
        <button
          v-for="(question, qIdx) in questions" 
          :key="qIdx"
          class="w-full text-left px-3 py-2 bg-white hover:bg-blue-50 hover:border-blue-300 rounded-lg border border-blue-200 text-sm text-gray-700 transition-all duration-200 shadow-sm hover:shadow-md"
          @click="$emit('selectQuestion', question)"
        >
          <div class="flex items-center justify-between">
            <span class="flex-1 pr-2">{{ question }}</span>
            <svg class="w-3 h-3 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/>
            </svg>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useExpandable } from '../../composables/useExpandable'

const { isExpanded, toggle } = useExpandable(false)

defineProps({
  questions: {
    type: Array,
    default: () => []
  }
})

defineEmits(['selectQuestion'])
</script> 