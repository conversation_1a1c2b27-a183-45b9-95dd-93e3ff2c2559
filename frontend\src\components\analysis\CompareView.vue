<template>
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div>
      <h3 class="text-lg font-medium text-gray-900 mb-4">模型1 分析</h3>
      <AnalysisResult :result="model1Result" />
    </div>
    <div>
      <h3 class="text-lg font-medium text-gray-900 mb-4">模型2 分析</h3>
      <AnalysisResult :result="model2Result" />
    </div>
  </div>
</template>

<script setup>
import AnalysisResult from './AnalysisResult.vue'

defineProps({
  model1Result: {
    type: String,
    required: true
  },
  model2Result: {
    type: String,
    required: true
  }
})
</script> 