<template>
  <AnalysisProgress
    :is-analyzing="isAnalyzing"
    :progress-message="progressMessage"
    :analysis-progress="analysisProgress"
  />
</template>

<script setup>
import AnalysisProgress from '../AnalysisProgress.vue'

defineProps({
  isAnalyzing: {
    type: Boolean,
    required: true
  },
  progressMessage: {
    type: String,
    required: true
  },
  analysisProgress: {
    type: Number,
    required: true
  }
})
</script> 