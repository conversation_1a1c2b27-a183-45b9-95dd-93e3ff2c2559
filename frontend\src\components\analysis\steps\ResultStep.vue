<template>
  <AnalysisResultPanel
    :analysis-result="analysisResult"
    @reset-analysis="$emit('reset-analysis')"
    @further-analysis="$emit('further-analysis')"
  />
</template>

<script setup>
import AnalysisResultPanel from '../AnalysisResultPanel.vue'

defineProps({
  analysisResult: {
    type: Object,
    required: true
  }
})

defineEmits(['reset-analysis', 'further-analysis'])
</script> 