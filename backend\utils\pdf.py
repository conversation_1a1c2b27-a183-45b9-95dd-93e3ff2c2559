import PyPDF2
import logging
from fastapi import HTTPException

# 配置日志
logger = logging.getLogger(__name__)

def read_pdf(file_path: str) -> str:
    try:
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
        return text
    except Exception as e:
        logger.error(f"PDF读取错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF读取错误: {str(e)}") 